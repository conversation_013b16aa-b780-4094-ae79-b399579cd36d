# Bank Account Registration System

A comprehensive multi-step bank account registration form built with PHP backend and React frontend, featuring 8 sequential tabs with strict validation, file uploads, and bilingual support (English/Arabic).

## Features

### 🏦 Banking-Grade Security
- CSRF protection
- Input sanitization and XSS prevention
- Secure file upload handling
- Data encryption for sensitive information
- Audit trail for all form submissions

### 📋 8-Step Registration Process
1. **Personal Information** - Name, gender, nationality, date of birth, marital status
2. **Place and Date of Birth** - Country/city of birth, birth certificate details
3. **Identity Information** - ID type, number, expiry, document upload
4. **Residence and Employment** - Address, employment status, income details
5. **Beneficial Owner Information** - Account purpose, source of funds, transaction volume
6. **Contact Information** - Phone, email, mailing address, communication preferences
7. **Political Influence** - PEP declaration, sanctions verification
8. **Signature** - Digital signature, terms acceptance, final review

### 🌐 Modern Frontend Features
- React with TypeScript
- Responsive design (desktop, tablet, mobile)
- Real-time validation with clear error messages
- Progress tracking and step navigation
- Bilingual support (Arabic RTL / English LTR)
- Professional banking UI/UX
- File drag-and-drop upload
- Form data persistence across sessions

### 🔧 Technical Stack
- **Backend**: PHP 7.4+ with PDO
- **Frontend**: React 18 + TypeScript + Vite
- **Database**: MySQL 5.7+
- **Styling**: Tailwind CSS
- **Icons**: Lucide React

## Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Node.js 16+ and npm (for frontend development)
- Web server (Apache/Nginx) or XAMPP for local development

### Backend Setup

1. **Database Setup**
   ```bash
   # Create database and import schema
   mysql -u root -p
   ```
   ```sql
   CREATE DATABASE bank_registration;
   USE bank_registration;
   SOURCE database/schema.sql;
   ```

2. **Configure Database Connection**
   Edit `config/database.php` and update the database credentials:
   ```php
   private $host = 'localhost';
   private $db_name = 'bank_registration';
   private $username = 'your_username';
   private $password = 'your_password';
   ```

3. **Set File Permissions**
   ```bash
   # Create uploads directory and set permissions
   mkdir uploads
   chmod 755 uploads
   
   # Ensure PHP can write to uploads directory
   chown www-data:www-data uploads  # On Linux
   ```

4. **Web Server Configuration**
   
   **For Apache (.htaccess)**:
   ```apache
   RewriteEngine On
   RewriteCond %{REQUEST_FILENAME} !-f
   RewriteCond %{REQUEST_FILENAME} !-d
   RewriteRule ^api/(.*)$ api/form-handler.php [QSA,L]
   ```

   **For Nginx**:
   ```nginx
   location /api/ {
       try_files $uri $uri/ /api/form-handler.php?$query_string;
   }
   ```

### Frontend Setup

1. **Install Dependencies**
   ```bash
   cd frontend
   npm install
   ```

2. **Development Server**
   ```bash
   npm run dev
   ```
   The frontend will be available at `http://localhost:5173`

3. **Production Build**
   ```bash
   npm run build
   ```
   Built files will be in the `frontend/dist` directory.

### XAMPP Setup (Windows)

1. **Install XAMPP** and start Apache and MySQL services

2. **Place Project Files**
   ```
   C:\xampp\htdocs\RegisterOnline\
   ├── api/
   ├── config/
   ├── database/
   ├── includes/
   ├── uploads/
   └── frontend/
   ```

3. **Import Database**
   - Open phpMyAdmin (`http://localhost/phpmyadmin`)
   - Create database `bank_registration`
   - Import `database/schema.sql`

4. **Access Application**
   - Backend API: `http://localhost/RegisterOnline/api/`
   - Frontend: `http://localhost:5173` (after running `npm run dev`)

## API Endpoints

### Authentication & Session
- `POST /api/start` - Start new registration session
- `GET /api/csrf-token` - Get CSRF token

### Form Management
- `POST /api/save-step` - Save step data
- `GET /api/get-data` - Retrieve form data
- `POST /api/upload-file` - Upload files
- `POST /api/submit-form` - Submit complete form

### Request/Response Format

**Save Step Request**:
```json
{
  "session_id": "abc123...",
  "step": 1,
  "data": {
    "first_name": "John",
    "last_name": "Doe",
    "gender": "male"
  }
}
```

**Success Response**:
```json
{
  "success": true,
  "message": "Step saved successfully",
  "data": null
}
```

**Error Response**:
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "first_name": ["The first name field is required."],
    "email": ["Please enter a valid email address."]
  }
}
```

## Security Features

### CSRF Protection
- All state-changing requests require CSRF token
- Tokens are generated per session and validated server-side

### Input Validation
- Server-side validation for all inputs
- Client-side validation for immediate feedback
- SQL injection prevention using prepared statements
- XSS protection through input sanitization

### File Upload Security
- File type validation (whitelist approach)
- File size limits (5MB default)
- MIME type verification
- Secure filename generation
- Virus scanning (recommended for production)

### Data Protection
- Sensitive data encryption
- Secure session management
- Audit logging for compliance
- GDPR-compliant data handling

## Customization

### Adding New Form Fields

1. **Update Database Schema**
   ```sql
   ALTER TABLE personal_info ADD COLUMN new_field VARCHAR(100);
   ```

2. **Update Validation Rules** in `config/database.php`:
   ```php
   'personal_info' => [
       'new_field' => ['required', 'string', 'max:100']
   ]
   ```

3. **Update Frontend Component**:
   ```tsx
   <FormInput
     label="New Field"
     name="new_field"
     value={formData.new_field || ''}
     onChange={(value) => handleChange('new_field', value)}
     required
   />
   ```

### Styling Customization

The application uses Tailwind CSS. Customize colors in `frontend/tailwind.config.js`:

```javascript
theme: {
  extend: {
    colors: {
      primary: {
        // Your brand colors
      }
    }
  }
}
```

### Language Support

Add new languages by extending the translations in `frontend/src/contexts/LanguageContext.tsx`.

## Production Deployment

### Security Checklist
- [ ] Change default database credentials
- [ ] Enable HTTPS/SSL
- [ ] Configure proper file permissions
- [ ] Set up regular database backups
- [ ] Enable error logging
- [ ] Configure rate limiting
- [ ] Set up monitoring and alerts

### Performance Optimization
- [ ] Enable PHP OPcache
- [ ] Configure database indexing
- [ ] Set up CDN for static assets
- [ ] Enable gzip compression
- [ ] Optimize images and file sizes

### Compliance
- [ ] Implement data retention policies
- [ ] Set up audit log monitoring
- [ ] Configure GDPR compliance features
- [ ] Implement backup and recovery procedures

## Troubleshooting

### Common Issues

**CORS Errors**:
- Ensure the frontend proxy is configured correctly in `vite.config.ts`
- Check that the backend CORS headers match your frontend URL

**File Upload Failures**:
- Check PHP `upload_max_filesize` and `post_max_size` settings
- Verify uploads directory permissions
- Ensure sufficient disk space

**Database Connection Issues**:
- Verify database credentials in `config/database.php`
- Check MySQL service is running
- Confirm database exists and schema is imported

**Session Issues**:
- Clear browser localStorage and cookies
- Check PHP session configuration
- Verify CSRF token generation

## License

This project is licensed under the MIT License. See LICENSE file for details.

## Support

For technical support or questions, please create an issue in the project repository.
