<?php
/**
 * Validation Class for Bank Registration Form
 */

class Validator {
    private $errors = [];
    private $data = [];
    
    public function __construct($data = []) {
        $this->data = $data;
    }
    
    public function validate($rules) {
        foreach ($rules as $field => $fieldRules) {
            $this->validateField($field, $fieldRules);
        }
        return empty($this->errors);
    }
    
    private function validateField($field, $rules) {
        $value = $this->data[$field] ?? null;
        
        foreach ($rules as $rule) {
            if (is_string($rule)) {
                $this->applyRule($field, $value, $rule);
            } elseif (is_array($rule)) {
                $this->applyRule($field, $value, $rule[0], $rule[1] ?? null);
            }
        }
    }
    
    private function applyRule($field, $value, $rule, $parameter = null) {
        switch ($rule) {
            case 'required':
                if (empty($value) && $value !== '0') {
                    $this->addError($field, "The {$field} field is required.");
                }
                break;
                
            case 'string':
                if (!is_string($value) && !is_null($value)) {
                    $this->addError($field, "The {$field} field must be a string.");
                }
                break;
                
            case 'email':
                if (!filter_var($value, FILTER_VALIDATE_EMAIL) && !empty($value)) {
                    $this->addError($field, "The {$field} field must be a valid email address.");
                }
                break;
                
            case 'date':
                if (!$this->isValidDate($value) && !empty($value)) {
                    $this->addError($field, "The {$field} field must be a valid date.");
                }
                break;
                
            case 'boolean':
                if (!is_bool($value) && !in_array($value, ['0', '1', 'true', 'false']) && !is_null($value)) {
                    $this->addError($field, "The {$field} field must be true or false.");
                }
                break;
                
            case 'accepted':
                if (!in_array($value, [true, 1, '1', 'true', 'on', 'yes'])) {
                    $this->addError($field, "The {$field} field must be accepted.");
                }
                break;
                
            case 'max':
                if (is_string($value) && strlen($value) > $parameter) {
                    $this->addError($field, "The {$field} field may not be greater than {$parameter} characters.");
                }
                break;
                
            case 'min':
                if (is_string($value) && strlen($value) < $parameter) {
                    $this->addError($field, "The {$field} field must be at least {$parameter} characters.");
                }
                break;
                
            case 'in':
                $allowedValues = explode(',', $parameter);
                if (!in_array($value, $allowedValues) && !empty($value)) {
                    $this->addError($field, "The selected {$field} is invalid.");
                }
                break;
                
            case 'before':
                if ($parameter === 'today' && !empty($value)) {
                    $date = new DateTime($value);
                    $today = new DateTime();
                    if ($date >= $today) {
                        $this->addError($field, "The {$field} field must be before today.");
                    }
                }
                break;
                
            case 'after':
                if ($parameter === 'today' && !empty($value)) {
                    $date = new DateTime($value);
                    $today = new DateTime();
                    if ($date <= $today) {
                        $this->addError($field, "The {$field} field must be after today.");
                    }
                }
                break;
        }
    }
    
    private function isValidDate($date) {
        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }
    
    private function addError($field, $message) {
        if (!isset($this->errors[$field])) {
            $this->errors[$field] = [];
        }
        $this->errors[$field][] = $message;
    }
    
    public function getErrors() {
        return $this->errors;
    }
    
    public function hasErrors() {
        return !empty($this->errors);
    }
}

/**
 * Security Utilities
 */
class SecurityUtils {
    public static function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([self::class, 'sanitizeInput'], $data);
        }
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }
    
    public static function validateFileUpload($file) {
        $errors = [];
        
        // Check if file was uploaded
        if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
            $errors[] = 'No file was uploaded.';
            return $errors;
        }
        
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $errors[] = 'File upload error: ' . $file['error'];
            return $errors;
        }
        
        // Check file size
        if ($file['size'] > Config::MAX_FILE_SIZE) {
            $errors[] = 'File size exceeds maximum allowed size of ' . (Config::MAX_FILE_SIZE / 1024 / 1024) . 'MB.';
        }
        
        // Check file type
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, Config::ALLOWED_FILE_TYPES)) {
            $errors[] = 'File type not allowed. Allowed types: ' . implode(', ', Config::ALLOWED_FILE_TYPES);
        }
        
        // Check if file is actually an image or PDF
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $allowedMimeTypes = [
            'image/jpeg',
            'image/png',
            'application/pdf'
        ];
        
        if (!in_array($mimeType, $allowedMimeTypes)) {
            $errors[] = 'Invalid file type detected.';
        }
        
        return $errors;
    }
    
    public static function generateSecureFilename($originalName) {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        return uniqid('upload_', true) . '.' . $extension;
    }
    
    public static function logAuditTrail($registrationId, $action, $stepNumber = null, $oldData = null, $newData = null) {
        try {
            $db = new Database();
            $pdo = $db->getConnection();
            
            $stmt = $pdo->prepare("
                INSERT INTO audit_log (registration_id, action, step_number, old_data, new_data, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $registrationId,
                $action,
                $stepNumber,
                $oldData ? json_encode($oldData) : null,
                $newData ? json_encode($newData) : null,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
        } catch (Exception $e) {
            error_log("Audit trail logging failed: " . $e->getMessage());
        }
    }
    
    public static function generateSessionId() {
        return bin2hex(random_bytes(32));
    }
    
    public static function isValidSessionId($sessionId) {
        return preg_match('/^[a-f0-9]{64}$/', $sessionId);
    }
}

/**
 * Response Helper
 */
class ResponseHelper {
    public static function json($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
    
    public static function success($data = null, $message = 'Success') {
        self::json([
            'success' => true,
            'message' => $message,
            'data' => $data
        ]);
    }
    
    public static function error($message, $errors = null, $statusCode = 400) {
        self::json([
            'success' => false,
            'message' => $message,
            'errors' => $errors
        ], $statusCode);
    }
    
    public static function validationError($errors) {
        self::error('Validation failed', $errors, 422);
    }
}
