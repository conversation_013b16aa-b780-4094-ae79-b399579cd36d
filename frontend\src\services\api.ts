import axios, { AxiosInstance, AxiosResponse } from 'axios'

interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  errors?: Record<string, string[]>
}

interface StartRegistrationResponse {
  session_id: string
  registration_id: number
  csrf_token: string
}

interface FormDataResponse {
  current_step: number
  data: Record<string, any>
}

interface FileUploadResponse {
  filename: string
  file_path: string
}

class ApiService {
  private api: AxiosInstance

  constructor() {
    this.api = axios.create({
      baseURL: '/api',
      timeout: 30000,
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Request interceptor to add CSRF token
    this.api.interceptors.request.use(
      (config) => {
        const csrfToken = this.getStoredCSRFToken()
        if (csrfToken && ['post', 'put', 'delete'].includes(config.method || '')) {
          config.headers['X-CSRF-Token'] = csrfToken
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response
      },
      (error) => {
        if (error.response?.status === 403) {
          // CSRF token expired, try to refresh
          this.refreshCSRFToken()
        }
        
        const message = error.response?.data?.message || 'An error occurred'
        const errors = error.response?.data?.errors || null
        
        return Promise.reject({
          message,
          errors,
          status: error.response?.status
        })
      }
    )
  }

  private getStoredCSRFToken(): string | null {
    return localStorage.getItem('csrf_token')
  }

  private storeCSRFToken(token: string): void {
    localStorage.setItem('csrf_token', token)
  }

  private async refreshCSRFToken(): Promise<void> {
    try {
      const response = await this.api.get<ApiResponse<{ csrf_token: string }>>('/csrf-token')
      if (response.data.success && response.data.data?.csrf_token) {
        this.storeCSRFToken(response.data.data.csrf_token)
      }
    } catch (error) {
      console.error('Failed to refresh CSRF token:', error)
    }
  }

  async startRegistration(): Promise<StartRegistrationResponse> {
    const response = await this.api.post<ApiResponse<StartRegistrationResponse>>('/start')
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.message || 'Failed to start registration')
    }

    // Store CSRF token
    this.storeCSRFToken(response.data.data.csrf_token)

    return response.data.data
  }

  async saveStep(
    sessionId: string,
    step: number,
    data: Record<string, any>,
    csrfToken: string
  ): Promise<void> {
    const response = await this.api.post<ApiResponse>('/save-step', {
      session_id: sessionId,
      step,
      data
    })

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to save step')
    }
  }

  async getFormData(sessionId: string): Promise<FormDataResponse> {
    const response = await this.api.get<ApiResponse<FormDataResponse>>(
      `/get-data?session_id=${encodeURIComponent(sessionId)}`
    )

    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.message || 'Failed to get form data')
    }

    return response.data.data
  }

  async uploadFile(
    file: File,
    fileType: string,
    step: number,
    sessionId: string
  ): Promise<FileUploadResponse> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('file_type', fileType)
    formData.append('step', step.toString())
    formData.append('session_id', sessionId)

    const response = await this.api.post<ApiResponse<FileUploadResponse>>(
      '/upload-file',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    )

    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.message || 'Failed to upload file')
    }

    return response.data.data
  }

  async submitForm(sessionId: string, csrfToken: string): Promise<{ registration_id: number }> {
    const response = await this.api.post<ApiResponse<{ registration_id: number }>>('/submit-form', {
      session_id: sessionId
    })

    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.message || 'Failed to submit form')
    }

    return response.data.data
  }

  async getCSRFToken(): Promise<string> {
    const response = await this.api.get<ApiResponse<{ csrf_token: string }>>('/csrf-token')
    
    if (!response.data.success || !response.data.data?.csrf_token) {
      throw new Error('Failed to get CSRF token')
    }

    const token = response.data.data.csrf_token
    this.storeCSRFToken(token)
    return token
  }
}

export const apiService = new ApiService()

// Validation utilities
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/
  return phoneRegex.test(phone)
}

export const validateDate = (date: string): boolean => {
  const dateObj = new Date(date)
  return dateObj instanceof Date && !isNaN(dateObj.getTime())
}

export const validateRequired = (value: any): boolean => {
  if (typeof value === 'string') {
    return value.trim().length > 0
  }
  if (typeof value === 'boolean') {
    return value === true
  }
  return value != null && value !== ''
}

export const formatDate = (date: string): string => {
  try {
    return new Date(date).toISOString().split('T')[0]
  } catch {
    return date
  }
}

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export const getFileExtension = (filename: string): string => {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)
}

export const isValidFileType = (filename: string, allowedTypes: string[]): boolean => {
  const extension = getFileExtension(filename).toLowerCase()
  return allowedTypes.includes(extension)
}
