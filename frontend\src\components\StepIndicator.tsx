import React from 'react'
import { useLanguage } from '../contexts/LanguageContext'
import { Check } from 'lucide-react'

interface StepIndicatorProps {
  currentStep: number
  totalSteps: number
  completedSteps: number[]
  onStepClick?: (step: number) => void
}

const StepIndicator: React.FC<StepIndicatorProps> = ({
  currentStep,
  totalSteps,
  completedSteps,
  onStepClick
}) => {
  const { t, isRTL } = useLanguage()

  const getStepStatus = (step: number) => {
    if (completedSteps.includes(step)) return 'completed'
    if (step === currentStep) return 'current'
    if (step < currentStep) return 'completed'
    return 'pending'
  }

  const getStepTitle = (step: number) => {
    return t(`step.${step}.title`)
  }

  const canClickStep = (step: number) => {
    return step === 1 || completedSteps.includes(step - 1) || step <= currentStep
  }

  const progressPercentage = ((currentStep - 1) / (totalSteps - 1)) * 100

  return (
    <div className="w-full">
      {/* Progress Bar */}
      <div className="relative mb-8">
        <div className="absolute top-4 left-0 w-full h-1 bg-gray-200 rounded-full">
          <div 
            className="progress-bar h-full rounded-full transition-all duration-500"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>

      {/* Desktop Step Indicators */}
      <div className="hidden md:flex justify-between items-start relative">
        {Array.from({ length: totalSteps }, (_, index) => {
          const step = index + 1
          const status = getStepStatus(step)
          const clickable = canClickStep(step) && onStepClick

          return (
            <div
              key={step}
              className={`flex flex-col items-center cursor-pointer group ${
                clickable ? 'hover:opacity-80' : ''
              }`}
              onClick={() => clickable && onStepClick(step)}
              style={{ width: `${100 / totalSteps}%` }}
            >
              {/* Step Circle */}
              <div
                className={`
                  step-indicator ${status}
                  ${!clickable ? 'cursor-not-allowed' : ''}
                  relative z-10 mb-2
                `}
              >
                {status === 'completed' ? (
                  <Check size={16} />
                ) : (
                  <span className="text-xs font-medium">{step}</span>
                )}
              </div>

              {/* Step Title */}
              <div className="text-center max-w-24">
                <p
                  className={`text-xs font-medium leading-tight ${
                    status === 'current'
                      ? 'text-primary-600'
                      : status === 'completed'
                      ? 'text-green-600'
                      : 'text-gray-500'
                  }`}
                >
                  {getStepTitle(step)}
                </p>
              </div>
            </div>
          )
        })}
      </div>

      {/* Mobile Step Indicators */}
      <div className="md:hidden">
        <div className="flex items-center justify-center space-x-2 mb-4">
          {Array.from({ length: totalSteps }, (_, index) => {
            const step = index + 1
            const status = getStepStatus(step)

            return (
              <div
                key={step}
                className={`
                  w-3 h-3 rounded-full transition-all duration-300
                  ${status === 'completed' ? 'bg-green-500' : ''}
                  ${status === 'current' ? 'bg-primary-600 scale-125' : ''}
                  ${status === 'pending' ? 'bg-gray-300' : ''}
                `}
              />
            )
          })}
        </div>

        {/* Current Step Info */}
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-1">
            {getStepTitle(currentStep)}
          </h3>
          <p className="text-sm text-gray-500">
            {t('progress.step_of', { 
              current: currentStep.toString(), 
              total: totalSteps.toString() 
            })}
          </p>
        </div>
      </div>

      {/* Step Status Legend (Desktop only) */}
      <div className="hidden lg:flex justify-center mt-6 space-x-6 text-xs">
        <div className="flex items-center space-x-2">
          <div className="step-indicator completed">
            <Check size={12} />
          </div>
          <span className="text-gray-600">{t('progress.completed')}</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="step-indicator current">
            <span>•</span>
          </div>
          <span className="text-gray-600">{t('progress.current')}</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="step-indicator pending">
            <span>•</span>
          </div>
          <span className="text-gray-600">{t('progress.pending')}</span>
        </div>
      </div>
    </div>
  )
}

export default StepIndicator
