import React, { useEffect, useState } from 'react'
import { useLanguage } from '../../contexts/LanguageContext'
import FormInput from '../FormInput'
import FormSelect from '../FormSelect'
import FormCheckbox from '../FormCheckbox'

interface ContactInfoData {
  primary_phone?: string
  secondary_phone?: string
  email?: string
  preferred_communication?: 'email' | 'phone' | 'sms' | 'mail'
  mailing_address_different?: boolean
  mailing_street_address?: string
  mailing_city?: string
  mailing_postal_code?: string
  mailing_country?: string
}

interface ContactInfoStepProps {
  data: ContactInfoData
  onChange: (data: ContactInfoData) => void
  errors: Record<string, string[]>
}

const ContactInfoStep: React.FC<ContactInfoStepProps> = ({
  data,
  onChange,
  errors
}) => {
  const { t } = useLanguage()
  const [formData, setFormData] = useState<ContactInfoData>(data)

  useEffect(() => {
    setFormData(data)
  }, [data])

  useEffect(() => {
    onChange(formData)
  }, [formData, onChange])

  const handleChange = (field: keyof ContactInfoData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const getFieldError = (field: string): string | undefined => {
    return errors[field]?.[0]
  }

  const communicationOptions = [
    { value: 'email', label: 'Email' },
    { value: 'phone', label: 'Phone Call' },
    { value: 'sms', label: 'SMS/Text Message' },
    { value: 'mail', label: 'Postal Mail' }
  ]

  const countryOptions = [
    { value: 'saudi_arabia', label: 'Saudi Arabia' },
    { value: 'united_arab_emirates', label: 'United Arab Emirates' },
    { value: 'kuwait', label: 'Kuwait' },
    { value: 'qatar', label: 'Qatar' },
    { value: 'bahrain', label: 'Bahrain' },
    { value: 'oman', label: 'Oman' },
    // Add more countries as needed
  ].sort((a, b) => a.label.localeCompare(b.label))

  return (
    <div className="space-y-8">
      {/* Contact Information */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 border-b pb-2">
          Contact Information
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormInput
            label={t('contact_info.primary_phone')}
            name="primary_phone"
            type="tel"
            value={formData.primary_phone || ''}
            onChange={(value) => handleChange('primary_phone', value)}
            error={getFieldError('primary_phone')}
            required
            maxLength={20}
            placeholder="+****************"
          />

          <FormInput
            label="Secondary Phone (Optional)"
            name="secondary_phone"
            type="tel"
            value={formData.secondary_phone || ''}
            onChange={(value) => handleChange('secondary_phone', value)}
            error={getFieldError('secondary_phone')}
            maxLength={20}
            placeholder="+****************"
          />
        </div>

        <FormInput
          label={t('contact_info.email')}
          name="email"
          type="email"
          value={formData.email || ''}
          onChange={(value) => handleChange('email', value)}
          error={getFieldError('email')}
          required
          maxLength={255}
          placeholder="<EMAIL>"
        />

        <FormSelect
          label="Preferred Communication Method"
          name="preferred_communication"
          value={formData.preferred_communication || ''}
          onChange={(value) => handleChange('preferred_communication', value as ContactInfoData['preferred_communication'])}
          options={communicationOptions}
          error={getFieldError('preferred_communication')}
          required
          placeholder="Select preferred method"
          className="md:w-1/2"
        />
      </div>

      {/* Mailing Address */}
      <div className="space-y-6">
        <div className="border-b pb-2">
          <h3 className="text-lg font-medium text-gray-900">Mailing Address</h3>
          <p className="text-sm text-gray-600 mt-1">
            Where should we send your account statements and correspondence?
          </p>
        </div>

        <FormCheckbox
          label="My mailing address is different from my residence address"
          name="mailing_address_different"
          checked={formData.mailing_address_different || false}
          onChange={(value) => handleChange('mailing_address_different', value)}
          error={getFieldError('mailing_address_different')}
          description="Check this if you want mail sent to a different address than your residence"
        />

        {formData.mailing_address_different && (
          <div className="space-y-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <FormInput
              label="Mailing Street Address"
              name="mailing_street_address"
              value={formData.mailing_street_address || ''}
              onChange={(value) => handleChange('mailing_street_address', value)}
              error={getFieldError('mailing_street_address')}
              required
              maxLength={255}
              placeholder="Enter mailing address"
            />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <FormInput
                label="City"
                name="mailing_city"
                value={formData.mailing_city || ''}
                onChange={(value) => handleChange('mailing_city', value)}
                error={getFieldError('mailing_city')}
                required
                maxLength={100}
              />

              <FormInput
                label="Postal Code"
                name="mailing_postal_code"
                value={formData.mailing_postal_code || ''}
                onChange={(value) => handleChange('mailing_postal_code', value)}
                error={getFieldError('mailing_postal_code')}
                required
                maxLength={20}
              />

              <FormSelect
                label="Country"
                name="mailing_country"
                value={formData.mailing_country || ''}
                onChange={(value) => handleChange('mailing_country', value)}
                options={countryOptions}
                error={getFieldError('mailing_country')}
                required
                placeholder="Select country"
              />
            </div>
          </div>
        )}
      </div>

      {/* Communication Preferences Note */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h4 className="text-sm font-medium text-green-800">Communication Preferences</h4>
            <div className="mt-1 text-sm text-green-700">
              <ul className="list-disc list-inside space-y-1">
                <li>We will use your preferred method for non-urgent communications</li>
                <li>Important security alerts will always be sent via multiple channels</li>
                <li>You can update your communication preferences anytime after account opening</li>
                <li>We respect your privacy and will never share your contact information</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ContactInfoStep
