import React, { useEffect, useState } from 'react'
import { useLanguage } from '../../contexts/LanguageContext'
import FormInput from '../FormInput'
import FormSelect from '../FormSelect'
import FileUpload from '../FileUpload'

interface IdentityInfoData {
  id_type?: 'national_id' | 'passport' | 'drivers_license'
  id_number?: string
  id_expiry_date?: string
  issuing_authority?: string
  id_document_path?: string
}

interface IdentityInfoStepProps {
  data: IdentityInfoData
  onChange: (data: IdentityInfoData) => void
  errors: Record<string, string[]>
}

const IdentityInfoStep: React.FC<IdentityInfoStepProps> = ({
  data,
  onChange,
  errors
}) => {
  const { t } = useLanguage()
  const [formData, setFormData] = useState<IdentityInfoData>(data)

  useEffect(() => {
    setFormData(data)
  }, [data])

  useEffect(() => {
    onChange(formData)
  }, [formData, onChange])

  const handleChange = (field: keyof IdentityInfoData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const getFieldError = (field: string): string | undefined => {
    return errors[field]?.[0]
  }

  const idTypeOptions = [
    { value: 'national_id', label: t('identity.id_type.national_id') },
    { value: 'passport', label: t('identity.id_type.passport') },
    { value: 'drivers_license', label: t('identity.id_type.drivers_license') }
  ]

  // Get minimum date (tomorrow) for expiry date
  const getMinExpiryDate = () => {
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    return tomorrow.toISOString().split('T')[0]
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormSelect
          label={t('identity.id_type')}
          name="id_type"
          value={formData.id_type || ''}
          onChange={(value) => handleChange('id_type', value as IdentityInfoData['id_type'])}
          options={idTypeOptions}
          error={getFieldError('id_type')}
          required
          placeholder={t('identity.id_type')}
        />

        <FormInput
          label={t('identity.id_number')}
          name="id_number"
          value={formData.id_number || ''}
          onChange={(value) => handleChange('id_number', value)}
          error={getFieldError('id_number')}
          required
          maxLength={50}
          placeholder="Enter ID number"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormInput
          label={t('identity.id_expiry_date')}
          name="id_expiry_date"
          type="date"
          value={formData.id_expiry_date || ''}
          onChange={(value) => handleChange('id_expiry_date', value)}
          error={getFieldError('id_expiry_date')}
          required
        />

        <FormInput
          label={t('identity.issuing_authority')}
          name="issuing_authority"
          value={formData.issuing_authority || ''}
          onChange={(value) => handleChange('issuing_authority', value)}
          error={getFieldError('issuing_authority')}
          required
          maxLength={100}
          placeholder="e.g., Ministry of Interior, Passport Office"
        />
      </div>

      <div className="space-y-4">
        <FileUpload
          label={t('identity.document_upload')}
          name="id_document"
          fileType="id_document"
          step={3}
          value={formData.id_document_path || ''}
          onChange={(filePath) => handleChange('id_document_path', filePath)}
          error={getFieldError('id_document_path')}
          required
          accept=".jpg,.jpeg,.png,.pdf"
          maxSize={5 * 1024 * 1024} // 5MB
        />
      </div>

      {/* Security Notice */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h4 className="text-sm font-medium text-green-800">Security & Privacy</h4>
            <div className="mt-1 text-sm text-green-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Your identity documents are encrypted and stored securely</li>
                <li>Only authorized personnel can access your documents for verification</li>
                <li>Documents are automatically deleted after account verification is complete</li>
                <li>Ensure the document is clear and all information is visible</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Document Requirements */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h4 className="text-sm font-medium text-blue-800">Document Requirements</h4>
            <div className="mt-1 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Document must be valid and not expired</li>
                <li>Image should be clear and high quality</li>
                <li>All text and details must be clearly visible</li>
                <li>Accepted formats: JPG, PNG, PDF (max 5MB)</li>
                <li>Both sides of the document may be required</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default IdentityInfoStep
