import React, { useEffect, useState } from 'react'
import { useLanguage } from '../../contexts/LanguageContext'
import FormInput from '../FormInput'
import FormSelect from '../FormSelect'
import FormTextarea from '../FormTextarea'

interface ResidenceEmploymentData {
  street_address?: string
  city?: string
  postal_code?: string
  country?: string
  residence_type?: 'owned' | 'rented' | 'family'
  employment_status?: 'employed' | 'self_employed' | 'unemployed' | 'retired' | 'student'
  employer_name?: string
  employer_address?: string
  monthly_income_range?: '0-1000' | '1001-5000' | '5001-10000' | '10001-25000' | '25001-50000' | '50000+'
  job_title?: string
  industry?: string
}

interface ResidenceEmploymentStepProps {
  data: ResidenceEmploymentData
  onChange: (data: ResidenceEmploymentData) => void
  errors: Record<string, string[]>
}

const ResidenceEmploymentStep: React.FC<ResidenceEmploymentStepProps> = ({
  data,
  onChange,
  errors
}) => {
  const { t } = useLanguage()
  const [formData, setFormData] = useState<ResidenceEmploymentData>(data)

  useEffect(() => {
    setFormData(data)
  }, [data])

  useEffect(() => {
    onChange(formData)
  }, [formData, onChange])

  const handleChange = (field: keyof ResidenceEmploymentData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const getFieldError = (field: string): string | undefined => {
    return errors[field]?.[0]
  }

  const residenceTypeOptions = [
    { value: 'owned', label: t('residence.residence_type.owned') },
    { value: 'rented', label: t('residence.residence_type.rented') },
    { value: 'family', label: t('residence.residence_type.family') }
  ]

  const employmentStatusOptions = [
    { value: 'employed', label: t('employment.status.employed') },
    { value: 'self_employed', label: t('employment.status.self_employed') },
    { value: 'unemployed', label: t('employment.status.unemployed') },
    { value: 'retired', label: t('employment.status.retired') },
    { value: 'student', label: t('employment.status.student') }
  ]

  const incomeRangeOptions = [
    { value: '0-1000', label: '$0 - $1,000' },
    { value: '1001-5000', label: '$1,001 - $5,000' },
    { value: '5001-10000', label: '$5,001 - $10,000' },
    { value: '10001-25000', label: '$10,001 - $25,000' },
    { value: '25001-50000', label: '$25,001 - $50,000' },
    { value: '50000+', label: '$50,000+' }
  ]

  const countryOptions = [
    { value: 'saudi_arabia', label: 'Saudi Arabia' },
    { value: 'united_arab_emirates', label: 'United Arab Emirates' },
    { value: 'kuwait', label: 'Kuwait' },
    { value: 'qatar', label: 'Qatar' },
    { value: 'bahrain', label: 'Bahrain' },
    { value: 'oman', label: 'Oman' },
    // Add more countries as needed
  ].sort((a, b) => a.label.localeCompare(b.label))

  const showEmploymentFields = formData.employment_status && 
    ['employed', 'self_employed'].includes(formData.employment_status)

  return (
    <div className="space-y-8">
      {/* Residence Information */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 border-b pb-2">
          Residence Information
        </h3>
        
        <FormInput
          label={t('residence.street_address')}
          name="street_address"
          value={formData.street_address || ''}
          onChange={(value) => handleChange('street_address', value)}
          error={getFieldError('street_address')}
          required
          maxLength={255}
          placeholder="Enter full street address"
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <FormInput
            label={t('residence.city')}
            name="city"
            value={formData.city || ''}
            onChange={(value) => handleChange('city', value)}
            error={getFieldError('city')}
            required
            maxLength={100}
          />

          <FormInput
            label={t('residence.postal_code')}
            name="postal_code"
            value={formData.postal_code || ''}
            onChange={(value) => handleChange('postal_code', value)}
            error={getFieldError('postal_code')}
            required
            maxLength={20}
          />

          <FormSelect
            label={t('residence.country')}
            name="country"
            value={formData.country || ''}
            onChange={(value) => handleChange('country', value)}
            options={countryOptions}
            error={getFieldError('country')}
            required
            placeholder={t('residence.country')}
          />
        </div>

        <FormSelect
          label={t('residence.residence_type')}
          name="residence_type"
          value={formData.residence_type || ''}
          onChange={(value) => handleChange('residence_type', value as ResidenceEmploymentData['residence_type'])}
          options={residenceTypeOptions}
          error={getFieldError('residence_type')}
          required
          placeholder={t('residence.residence_type')}
          className="md:w-1/2"
        />
      </div>

      {/* Employment Information */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 border-b pb-2">
          Employment Information
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormSelect
            label={t('employment.status')}
            name="employment_status"
            value={formData.employment_status || ''}
            onChange={(value) => handleChange('employment_status', value as ResidenceEmploymentData['employment_status'])}
            options={employmentStatusOptions}
            error={getFieldError('employment_status')}
            required
            placeholder={t('employment.status')}
          />

          <FormSelect
            label={t('employment.monthly_income')}
            name="monthly_income_range"
            value={formData.monthly_income_range || ''}
            onChange={(value) => handleChange('monthly_income_range', value as ResidenceEmploymentData['monthly_income_range'])}
            options={incomeRangeOptions}
            error={getFieldError('monthly_income_range')}
            required
            placeholder="Select income range"
          />
        </div>

        {showEmploymentFields && (
          <div className="space-y-6 p-4 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormInput
                label={t('employment.employer_name')}
                name="employer_name"
                value={formData.employer_name || ''}
                onChange={(value) => handleChange('employer_name', value)}
                error={getFieldError('employer_name')}
                maxLength={200}
                placeholder="Company/Organization name"
              />

              <FormInput
                label={t('employment.job_title')}
                name="job_title"
                value={formData.job_title || ''}
                onChange={(value) => handleChange('job_title', value)}
                error={getFieldError('job_title')}
                maxLength={100}
                placeholder="Your job title"
              />
            </div>

            <FormInput
              label={t('employment.industry')}
              name="industry"
              value={formData.industry || ''}
              onChange={(value) => handleChange('industry', value)}
              error={getFieldError('industry')}
              maxLength={100}
              placeholder="e.g., Technology, Healthcare, Finance"
            />

            <FormTextarea
              label={t('employment.employer_address')}
              name="employer_address"
              value={formData.employer_address || ''}
              onChange={(value) => handleChange('employer_address', value)}
              error={getFieldError('employer_address')}
              rows={3}
              maxLength={500}
              placeholder="Full employer address"
            />
          </div>
        )}
      </div>
    </div>
  )
}

export default ResidenceEmploymentStep
