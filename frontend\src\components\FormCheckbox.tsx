import React from 'react'
import { useLanguage } from '../contexts/LanguageContext'
import { Check } from 'lucide-react'

interface FormCheckboxProps {
  label: string
  name: string
  checked: boolean
  onChange: (checked: boolean) => void
  error?: string
  required?: boolean
  disabled?: boolean
  className?: string
  description?: string
}

const FormCheckbox: React.FC<FormCheckboxProps> = ({
  label,
  name,
  checked,
  onChange,
  error,
  required = false,
  disabled = false,
  className = '',
  description
}) => {
  const { isRTL } = useLanguage()

  return (
    <div className={`${className}`}>
      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id={name}
            name={name}
            type="checkbox"
            checked={checked}
            onChange={(e) => onChange(e.target.checked)}
            disabled={disabled}
            required={required}
            className="sr-only"
          />
          <label
            htmlFor={name}
            className={`
              relative flex items-center justify-center w-5 h-5 border-2 rounded cursor-pointer transition-colors
              ${checked 
                ? 'bg-primary-600 border-primary-600' 
                : 'bg-white border-gray-300 hover:border-primary-500'
              }
              ${error ? 'border-red-300' : ''}
              ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
            `}
          >
            {checked && (
              <Check className="w-3 h-3 text-white" strokeWidth={3} />
            )}
          </label>
        </div>
        <div className={`${isRTL ? 'mr-3' : 'ml-3'} flex-1`}>
          <label
            htmlFor={name}
            className={`
              text-sm font-medium text-gray-700 cursor-pointer
              ${disabled ? 'cursor-not-allowed opacity-50' : ''}
            `}
          >
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
          {description && (
            <p className="text-xs text-gray-500 mt-1">{description}</p>
          )}
        </div>
      </div>
      {error && <p className="form-error mt-1">{error}</p>}
    </div>
  )
}

export default FormCheckbox
