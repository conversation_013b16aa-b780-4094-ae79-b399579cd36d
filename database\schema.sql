-- Bank Account Registration Database Schema
-- Created for comprehensive multi-step registration form

CREATE DATABASE IF NOT EXISTS bank_registration;
USE bank_registration;

-- Main registration table
CREATE TABLE registrations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id VARCHAR(255) UNIQUE NOT NULL,
    status ENUM('draft', 'completed', 'submitted', 'approved', 'rejected') DEFAULT 'draft',
    current_step INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    submitted_at TIMESTAMP NULL,
    ip_address VARCHAR(45),
    user_agent TEXT
);

-- Personal Information (Step 1)
CREATE TABLE personal_info (
    id INT PRIMARY KEY AUTO_INCREMENT,
    registration_id INT NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    middle_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    gender ENUM('male', 'female', 'other') NOT NULL,
    nationality VARCHAR(100) NOT NULL,
    date_of_birth DATE NOT NULL,
    marital_status ENUM('single', 'married', 'divorced', 'widowed') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE
);

-- Place and Date of Birth (Step 2)
CREATE TABLE birth_info (
    id INT PRIMARY KEY AUTO_INCREMENT,
    registration_id INT NOT NULL,
    country_of_birth VARCHAR(100) NOT NULL,
    city_of_birth VARCHAR(100) NOT NULL,
    birth_certificate_number VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE
);

-- Identity Information (Step 3)
CREATE TABLE identity_info (
    id INT PRIMARY KEY AUTO_INCREMENT,
    registration_id INT NOT NULL,
    id_type ENUM('national_id', 'passport', 'drivers_license') NOT NULL,
    id_number VARCHAR(50) NOT NULL,
    id_expiry_date DATE NOT NULL,
    issuing_authority VARCHAR(100) NOT NULL,
    id_document_path VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE
);

-- Residence and Employment (Step 4)
CREATE TABLE residence_employment (
    id INT PRIMARY KEY AUTO_INCREMENT,
    registration_id INT NOT NULL,
    street_address VARCHAR(255) NOT NULL,
    city VARCHAR(100) NOT NULL,
    postal_code VARCHAR(20) NOT NULL,
    country VARCHAR(100) NOT NULL,
    residence_type ENUM('owned', 'rented', 'family') NOT NULL,
    employment_status ENUM('employed', 'self_employed', 'unemployed', 'retired', 'student') NOT NULL,
    employer_name VARCHAR(200),
    employer_address TEXT,
    monthly_income_range ENUM('0-1000', '1001-5000', '5001-10000', '10001-25000', '25001-50000', '50000+') NOT NULL,
    job_title VARCHAR(100),
    industry VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE
);

-- Beneficial Owner Information (Step 5)
CREATE TABLE beneficial_owner (
    id INT PRIMARY KEY AUTO_INCREMENT,
    registration_id INT NOT NULL,
    account_purpose TEXT NOT NULL,
    is_beneficial_owner_different BOOLEAN DEFAULT FALSE,
    beneficial_owner_name VARCHAR(200),
    beneficial_owner_details TEXT,
    source_of_funds ENUM('salary', 'business', 'investment', 'inheritance', 'gift', 'other') NOT NULL,
    expected_transaction_volume ENUM('low', 'medium', 'high') NOT NULL,
    business_relationship_details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE
);

-- Contact Information (Step 6)
CREATE TABLE contact_info (
    id INT PRIMARY KEY AUTO_INCREMENT,
    registration_id INT NOT NULL,
    primary_phone VARCHAR(20) NOT NULL,
    secondary_phone VARCHAR(20),
    email VARCHAR(255) NOT NULL,
    preferred_communication ENUM('email', 'phone', 'sms', 'mail') NOT NULL,
    mailing_address_different BOOLEAN DEFAULT FALSE,
    mailing_street_address VARCHAR(255),
    mailing_city VARCHAR(100),
    mailing_postal_code VARCHAR(20),
    mailing_country VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE
);

-- Political Influence (Step 7)
CREATE TABLE political_influence (
    id INT PRIMARY KEY AUTO_INCREMENT,
    registration_id INT NOT NULL,
    is_pep BOOLEAN DEFAULT FALSE,
    pep_details TEXT,
    family_member_pep BOOLEAN DEFAULT FALSE,
    family_pep_details TEXT,
    government_position_history TEXT,
    political_party_affiliations TEXT,
    sanctions_list_check BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE
);

-- Signature and Final (Step 8)
CREATE TABLE signature_final (
    id INT PRIMARY KEY AUTO_INCREMENT,
    registration_id INT NOT NULL,
    digital_signature_path VARCHAR(255),
    terms_accepted BOOLEAN DEFAULT FALSE,
    privacy_policy_accepted BOOLEAN DEFAULT FALSE,
    final_review_completed BOOLEAN DEFAULT FALSE,
    submission_confirmed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE
);

-- File uploads tracking
CREATE TABLE file_uploads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    registration_id INT NOT NULL,
    file_type ENUM('id_document', 'signature', 'supporting_document') NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    upload_step INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE
);

-- Audit trail
CREATE TABLE audit_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    registration_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    step_number INT,
    old_data JSON,
    new_data JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE
);

-- Indexes for performance
CREATE INDEX idx_registrations_session ON registrations(session_id);
CREATE INDEX idx_registrations_status ON registrations(status);
CREATE INDEX idx_audit_log_registration ON audit_log(registration_id);
CREATE INDEX idx_file_uploads_registration ON file_uploads(registration_id);
