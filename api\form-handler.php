<?php
/**
 * Main API Handler for Bank Registration Form
 */

require_once '../config/database.php';
require_once '../includes/validation.php';

// Enable CORS for frontend
header('Access-Control-Allow-Origin: http://localhost:5173');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-CSRF-Token');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

class FormHandler {
    private $db;
    private $pdo;
    
    public function __construct() {
        $this->db = new Database();
        $this->pdo = $this->db->getConnection();
    }
    
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = $_SERVER['PATH_INFO'] ?? '/';
        
        // CSRF Protection
        if (Config::ENABLE_CSRF_PROTECTION && in_array($method, ['POST', 'PUT', 'DELETE'])) {
            $token = $_SERVER['HTTP_X_CSRF_TOKEN'] ?? '';
            if (!Config::validateCSRFToken($token)) {
                ResponseHelper::error('CSRF token validation failed', null, 403);
            }
        }
        
        try {
            switch ($path) {
                case '/start':
                    if ($method === 'POST') {
                        $this->startRegistration();
                    }
                    break;
                    
                case '/save-step':
                    if ($method === 'POST') {
                        $this->saveStep();
                    }
                    break;
                    
                case '/get-data':
                    if ($method === 'GET') {
                        $this->getData();
                    }
                    break;
                    
                case '/upload-file':
                    if ($method === 'POST') {
                        $this->uploadFile();
                    }
                    break;
                    
                case '/submit-form':
                    if ($method === 'POST') {
                        $this->submitForm();
                    }
                    break;
                    
                case '/csrf-token':
                    if ($method === 'GET') {
                        $this->getCSRFToken();
                    }
                    break;
                    
                default:
                    ResponseHelper::error('Endpoint not found', null, 404);
            }
        } catch (Exception $e) {
            error_log("API Error: " . $e->getMessage());
            ResponseHelper::error('Internal server error', null, 500);
        }
    }
    
    private function startRegistration() {
        $sessionId = SecurityUtils::generateSessionId();
        
        $stmt = $this->pdo->prepare("
            INSERT INTO registrations (session_id, ip_address, user_agent)
            VALUES (?, ?, ?)
        ");
        
        $stmt->execute([
            $sessionId,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
        
        $registrationId = $this->db->lastInsertId();
        
        SecurityUtils::logAuditTrail($registrationId, 'registration_started');
        
        ResponseHelper::success([
            'session_id' => $sessionId,
            'registration_id' => $registrationId,
            'csrf_token' => Config::generateCSRFToken()
        ], 'Registration started successfully');
    }
    
    private function saveStep() {
        $input = json_decode(file_get_contents('php://input'), true);
        $input = SecurityUtils::sanitizeInput($input);
        
        $sessionId = $input['session_id'] ?? '';
        $step = (int)($input['step'] ?? 0);
        $data = $input['data'] ?? [];
        
        if (!SecurityUtils::isValidSessionId($sessionId)) {
            ResponseHelper::error('Invalid session ID');
        }
        
        // Get registration ID
        $stmt = $this->pdo->prepare("SELECT id FROM registrations WHERE session_id = ?");
        $stmt->execute([$sessionId]);
        $registration = $stmt->fetch();
        
        if (!$registration) {
            ResponseHelper::error('Registration not found');
        }
        
        $registrationId = $registration['id'];
        
        // Validate step data
        $stepName = $this->getStepName($step);
        $rules = Config::VALIDATION_RULES[$stepName] ?? [];
        
        $validator = new Validator($data);
        if (!$validator->validate($rules)) {
            ResponseHelper::validationError($validator->getErrors());
        }
        
        // Save step data
        $this->saveStepData($registrationId, $step, $data);
        
        // Update current step
        $stmt = $this->pdo->prepare("UPDATE registrations SET current_step = ? WHERE id = ?");
        $stmt->execute([max($step, $this->getCurrentStep($registrationId)), $registrationId]);
        
        SecurityUtils::logAuditTrail($registrationId, 'step_saved', $step, null, $data);
        
        ResponseHelper::success(null, 'Step saved successfully');
    }
    
    private function getData() {
        $sessionId = $_GET['session_id'] ?? '';
        
        if (!SecurityUtils::isValidSessionId($sessionId)) {
            ResponseHelper::error('Invalid session ID');
        }
        
        $stmt = $this->pdo->prepare("SELECT id, current_step FROM registrations WHERE session_id = ?");
        $stmt->execute([$sessionId]);
        $registration = $stmt->fetch();
        
        if (!$registration) {
            ResponseHelper::error('Registration not found');
        }
        
        $data = $this->getAllStepData($registration['id']);
        
        ResponseHelper::success([
            'current_step' => $registration['current_step'],
            'data' => $data
        ]);
    }
    
    private function uploadFile() {
        $sessionId = $_POST['session_id'] ?? '';
        $fileType = $_POST['file_type'] ?? '';
        $step = (int)($_POST['step'] ?? 0);
        
        if (!SecurityUtils::isValidSessionId($sessionId)) {
            ResponseHelper::error('Invalid session ID');
        }
        
        if (!isset($_FILES['file'])) {
            ResponseHelper::error('No file uploaded');
        }
        
        $file = $_FILES['file'];
        $errors = SecurityUtils::validateFileUpload($file);
        
        if (!empty($errors)) {
            ResponseHelper::error('File validation failed', $errors);
        }
        
        // Get registration ID
        $stmt = $this->pdo->prepare("SELECT id FROM registrations WHERE session_id = ?");
        $stmt->execute([$sessionId]);
        $registration = $stmt->fetch();
        
        if (!$registration) {
            ResponseHelper::error('Registration not found');
        }
        
        $registrationId = $registration['id'];
        
        // Save file
        $filename = SecurityUtils::generateSecureFilename($file['name']);
        $uploadPath = Config::getUploadPath() . $filename;
        
        if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
            ResponseHelper::error('Failed to save file');
        }
        
        // Save file record
        $stmt = $this->pdo->prepare("
            INSERT INTO file_uploads (registration_id, file_type, original_filename, stored_filename, file_path, file_size, mime_type, upload_step)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $uploadPath);
        finfo_close($finfo);
        
        $stmt->execute([
            $registrationId,
            $fileType,
            $file['name'],
            $filename,
            $uploadPath,
            $file['size'],
            $mimeType,
            $step
        ]);
        
        SecurityUtils::logAuditTrail($registrationId, 'file_uploaded', $step, null, [
            'file_type' => $fileType,
            'filename' => $filename
        ]);
        
        ResponseHelper::success([
            'filename' => $filename,
            'file_path' => 'uploads/' . $filename
        ], 'File uploaded successfully');
    }
    
    private function submitForm() {
        $input = json_decode(file_get_contents('php://input'), true);
        $sessionId = $input['session_id'] ?? '';
        
        if (!SecurityUtils::isValidSessionId($sessionId)) {
            ResponseHelper::error('Invalid session ID');
        }
        
        $stmt = $this->pdo->prepare("SELECT id FROM registrations WHERE session_id = ?");
        $stmt->execute([$sessionId]);
        $registration = $stmt->fetch();
        
        if (!$registration) {
            ResponseHelper::error('Registration not found');
        }
        
        $registrationId = $registration['id'];
        
        // Validate all steps are completed
        if (!$this->validateAllSteps($registrationId)) {
            ResponseHelper::error('Please complete all required steps');
        }
        
        // Update registration status
        $stmt = $this->pdo->prepare("
            UPDATE registrations 
            SET status = 'submitted', submitted_at = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$registrationId]);
        
        SecurityUtils::logAuditTrail($registrationId, 'form_submitted');
        
        ResponseHelper::success([
            'registration_id' => $registrationId
        ], 'Form submitted successfully');
    }
    
    private function getCSRFToken() {
        ResponseHelper::success([
            'csrf_token' => Config::generateCSRFToken()
        ]);
    }
    
    private function getStepName($step) {
        $stepNames = [
            1 => 'personal_info',
            2 => 'birth_info',
            3 => 'identity_info',
            4 => 'residence_employment',
            5 => 'beneficial_owner',
            6 => 'contact_info',
            7 => 'political_influence',
            8 => 'signature_final'
        ];

        return $stepNames[$step] ?? '';
    }

    private function getCurrentStep($registrationId) {
        $stmt = $this->pdo->prepare("SELECT current_step FROM registrations WHERE id = ?");
        $stmt->execute([$registrationId]);
        $result = $stmt->fetch();
        return $result ? $result['current_step'] : 1;
    }

    private function saveStepData($registrationId, $step, $data) {
        $stepName = $this->getStepName($step);
        $tableName = $stepName;

        // Check if record exists
        $stmt = $this->pdo->prepare("SELECT id FROM {$tableName} WHERE registration_id = ?");
        $stmt->execute([$registrationId]);
        $exists = $stmt->fetch();

        if ($exists) {
            $this->updateStepData($tableName, $registrationId, $data);
        } else {
            $this->insertStepData($tableName, $registrationId, $data);
        }
    }

    private function insertStepData($tableName, $registrationId, $data) {
        $data['registration_id'] = $registrationId;
        $columns = implode(', ', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));

        $stmt = $this->pdo->prepare("INSERT INTO {$tableName} ({$columns}) VALUES ({$placeholders})");
        $stmt->execute($data);
    }

    private function updateStepData($tableName, $registrationId, $data) {
        $setParts = [];
        foreach (array_keys($data) as $column) {
            $setParts[] = "{$column} = :{$column}";
        }
        $setClause = implode(', ', $setParts);

        $data['registration_id'] = $registrationId;

        $stmt = $this->pdo->prepare("UPDATE {$tableName} SET {$setClause} WHERE registration_id = :registration_id");
        $stmt->execute($data);
    }

    private function getAllStepData($registrationId) {
        $data = [];

        for ($step = 1; $step <= 8; $step++) {
            $stepName = $this->getStepName($step);
            $stmt = $this->pdo->prepare("SELECT * FROM {$stepName} WHERE registration_id = ?");
            $stmt->execute([$registrationId]);
            $stepData = $stmt->fetch();

            if ($stepData) {
                unset($stepData['id'], $stepData['registration_id'], $stepData['created_at'], $stepData['updated_at']);
                $data[$step] = $stepData;
            }
        }

        return $data;
    }

    private function validateAllSteps($registrationId) {
        // Check if all required steps have data
        $requiredSteps = [1, 2, 3, 4, 5, 6, 7, 8];

        foreach ($requiredSteps as $step) {
            $stepName = $this->getStepName($step);
            $stmt = $this->pdo->prepare("SELECT id FROM {$stepName} WHERE registration_id = ?");
            $stmt->execute([$registrationId]);

            if (!$stmt->fetch()) {
                return false;
            }
        }

        return true;
    }
}

// Initialize and handle request
$handler = new FormHandler();
$handler->handleRequest();
