import React from 'react'
import { useLanguage } from '../contexts/LanguageContext'

interface FormInputProps {
  label: string
  name: string
  type?: 'text' | 'email' | 'tel' | 'date' | 'number'
  value: string
  onChange: (value: string) => void
  error?: string
  required?: boolean
  placeholder?: string
  disabled?: boolean
  className?: string
  maxLength?: number
  pattern?: string
}

const FormInput: React.FC<FormInputProps> = ({
  label,
  name,
  type = 'text',
  value,
  onChange,
  error,
  required = false,
  placeholder,
  disabled = false,
  className = '',
  maxLength,
  pattern
}) => {
  const { isRTL } = useLanguage()

  return (
    <div className={`${className}`}>
      <label htmlFor={name} className="form-label">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      <input
        type={type}
        id={name}
        name={name}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        required={required}
        maxLength={maxLength}
        pattern={pattern}
        className={`
          form-input
          ${error ? 'form-input-error' : ''}
          ${isRTL ? 'text-right' : 'text-left'}
          ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}
        `}
        dir={isRTL ? 'rtl' : 'ltr'}
      />
      {error && <p className="form-error">{error}</p>}
    </div>
  )
}

export default FormInput
