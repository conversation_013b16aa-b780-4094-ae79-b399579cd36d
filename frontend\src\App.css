/* Additional custom styles */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-in-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.3s ease-in-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* File upload styles */
.file-upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.file-upload-area:hover {
  border-color: #3b82f6;
  background-color: #f8fafc;
}

.file-upload-area.dragover {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

/* Progress bar */
.progress-bar {
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  height: 4px;
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* Signature pad */
.signature-pad {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .step-indicator {
    width: 6px;
    height: 6px;
    font-size: 0;
  }
  
  .step-indicator.current,
  .step-indicator.completed {
    width: 8px;
    height: 8px;
  }
}
