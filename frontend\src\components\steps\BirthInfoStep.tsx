import React, { useEffect, useState } from 'react'
import { useLanguage } from '../../contexts/LanguageContext'
import FormInput from '../FormInput'
import FormSelect from '../FormSelect'

interface BirthInfoData {
  country_of_birth?: string
  city_of_birth?: string
  birth_certificate_number?: string
}

interface BirthInfoStepProps {
  data: BirthInfoData
  onChange: (data: BirthInfoData) => void
  errors: Record<string, string[]>
}

const BirthInfoStep: React.FC<BirthInfoStepProps> = ({
  data,
  onChange,
  errors
}) => {
  const { t } = useLanguage()
  const [formData, setFormData] = useState<BirthInfoData>(data)

  useEffect(() => {
    setFormData(data)
  }, [data])

  useEffect(() => {
    onChange(formData)
  }, [formData, onChange])

  const handleChange = (field: keyof BirthInfoData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const getFieldError = (field: string): string | undefined => {
    return errors[field]?.[0]
  }

  // Common countries - in a real app, this would come from an API
  const countryOptions = [
    { value: 'saudi_arabia', label: 'Saudi Arabia' },
    { value: 'united_arab_emirates', label: 'United Arab Emirates' },
    { value: 'kuwait', label: 'Kuwait' },
    { value: 'qatar', label: 'Qatar' },
    { value: 'bahrain', label: 'Bahrain' },
    { value: 'oman', label: 'Oman' },
    { value: 'jordan', label: 'Jordan' },
    { value: 'lebanon', label: 'Lebanon' },
    { value: 'syria', label: 'Syria' },
    { value: 'iraq', label: 'Iraq' },
    { value: 'egypt', label: 'Egypt' },
    { value: 'morocco', label: 'Morocco' },
    { value: 'tunisia', label: 'Tunisia' },
    { value: 'algeria', label: 'Algeria' },
    { value: 'libya', label: 'Libya' },
    { value: 'sudan', label: 'Sudan' },
    { value: 'yemen', label: 'Yemen' },
    { value: 'palestine', label: 'Palestine' },
    { value: 'united_states', label: 'United States' },
    { value: 'united_kingdom', label: 'United Kingdom' },
    { value: 'canada', label: 'Canada' },
    { value: 'australia', label: 'Australia' },
    { value: 'germany', label: 'Germany' },
    { value: 'france', label: 'France' },
    { value: 'italy', label: 'Italy' },
    { value: 'spain', label: 'Spain' },
    { value: 'india', label: 'India' },
    { value: 'pakistan', label: 'Pakistan' },
    { value: 'bangladesh', label: 'Bangladesh' },
    { value: 'philippines', label: 'Philippines' },
    { value: 'indonesia', label: 'Indonesia' },
    { value: 'malaysia', label: 'Malaysia' },
    { value: 'singapore', label: 'Singapore' },
    { value: 'thailand', label: 'Thailand' },
    { value: 'other', label: 'Other' }
  ].sort((a, b) => a.label.localeCompare(b.label))

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormSelect
          label={t('birth.country_of_birth')}
          name="country_of_birth"
          value={formData.country_of_birth || ''}
          onChange={(value) => handleChange('country_of_birth', value)}
          options={countryOptions}
          error={getFieldError('country_of_birth')}
          required
          placeholder={t('birth.country_of_birth')}
        />

        <FormInput
          label={t('birth.city_of_birth')}
          name="city_of_birth"
          value={formData.city_of_birth || ''}
          onChange={(value) => handleChange('city_of_birth', value)}
          error={getFieldError('city_of_birth')}
          required
          maxLength={100}
          placeholder="Enter city or province"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormInput
          label={t('birth.certificate_number')}
          name="birth_certificate_number"
          value={formData.birth_certificate_number || ''}
          onChange={(value) => handleChange('birth_certificate_number', value)}
          error={getFieldError('birth_certificate_number')}
          maxLength={50}
          placeholder="Optional"
        />
      </div>

      {/* Information Note */}
      <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-amber-700">
              <strong>Important:</strong> The birth information should match your official birth certificate or passport. 
              If you were born in a different country than your current nationality, please ensure accuracy as this may affect account verification.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BirthInfoStep
