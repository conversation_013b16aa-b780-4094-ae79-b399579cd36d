import React, { useRef, useState } from 'react'
import { useLanguage } from '../contexts/LanguageContext'
import { useForm } from '../contexts/FormContext'
import { Upload, File, X, Check } from 'lucide-react'
import { formatFileSize, isValidFileType } from '../services/api'

interface FileUploadProps {
  label: string
  name: string
  fileType: string
  step: number
  value?: string
  onChange: (filePath: string) => void
  error?: string
  required?: boolean
  disabled?: boolean
  className?: string
  accept?: string
  maxSize?: number // in bytes
}

const FileUpload: React.FC<FileUploadProps> = ({
  label,
  name,
  fileType,
  step,
  value,
  onChange,
  error,
  required = false,
  disabled = false,
  className = '',
  accept = '.jpg,.jpeg,.png,.pdf',
  maxSize = 5 * 1024 * 1024 // 5MB default
}) => {
  const { t } = useLanguage()
  const { uploadFile } = useForm()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadError, setUploadError] = useState<string | null>(null)

  const allowedTypes = accept.split(',').map(type => type.trim().replace('.', ''))

  const handleFileSelect = async (file: File) => {
    setUploadError(null)

    // Validate file type
    if (!isValidFileType(file.name, allowedTypes)) {
      setUploadError(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`)
      return
    }

    // Validate file size
    if (file.size > maxSize) {
      setUploadError(`File size exceeds ${formatFileSize(maxSize)}`)
      return
    }

    try {
      setIsUploading(true)
      const filePath = await uploadFile(file, fileType, step)
      onChange(filePath)
    } catch (error: any) {
      setUploadError(error.message || 'Failed to upload file')
    } finally {
      setIsUploading(false)
    }
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    const file = e.dataTransfer.files[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleRemoveFile = () => {
    onChange('')
    setUploadError(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const openFileDialog = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  return (
    <div className={`${className}`}>
      <label className="form-label">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>

      {value ? (
        // File uploaded state
        <div className="border border-green-200 bg-green-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <Check className="w-5 h-5 text-green-500" />
              </div>
              <div>
                <p className="text-sm font-medium text-green-800">
                  {t('common.file_uploaded')}
                </p>
                <p className="text-xs text-green-600">
                  {value.split('/').pop()}
                </p>
              </div>
            </div>
            {!disabled && (
              <button
                type="button"
                onClick={handleRemoveFile}
                className="text-green-600 hover:text-green-800 transition-colors"
                title={t('common.remove_file')}
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>
      ) : (
        // Upload area
        <div
          className={`
            file-upload-area
            ${isDragging ? 'dragover' : ''}
            ${error || uploadError ? 'border-red-300 bg-red-50' : ''}
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
          `}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={openFileDialog}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept={accept}
            onChange={handleFileInputChange}
            disabled={disabled}
            className="hidden"
          />

          <div className="flex flex-col items-center">
            {isUploading ? (
              <>
                <div className="w-8 h-8 border-2 border-primary-200 border-t-primary-600 rounded-full animate-spin mb-3" />
                <p className="text-sm text-gray-600">{t('common.loading')}</p>
              </>
            ) : (
              <>
                <Upload className="w-8 h-8 text-gray-400 mb-3" />
                <p className="text-sm font-medium text-gray-900 mb-1">
                  {t('common.upload_file')}
                </p>
                <p className="text-xs text-gray-500 text-center">
                  Drag and drop or click to select<br />
                  Max size: {formatFileSize(maxSize)}<br />
                  Allowed: {allowedTypes.join(', ')}
                </p>
              </>
            )}
          </div>
        </div>
      )}

      {(error || uploadError) && (
        <p className="form-error">{error || uploadError}</p>
      )}
    </div>
  )
}

export default FileUpload
