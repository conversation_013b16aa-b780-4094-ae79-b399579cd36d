# Bank Registration System - Apache Configuration

# Enable URL Rewriting
RewriteEngine On

# Security Headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com; img-src 'self' data:; connect-src 'self'"

# CORS Headers for API
<IfModule mod_headers.c>
    SetEnvIf Origin "http(s)?://(localhost:5173|localhost:3000)$" CORS_ALLOW_ORIGIN=$1
    Header always set Access-Control-Allow-Origin "%{CORS_ALLOW_ORIGIN}e" env=CORS_ALLOW_ORIGIN
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-CSRF-Token"
    Header always set Access-Control-Allow-Credentials "true"
</IfModule>

# Handle preflight OPTIONS requests
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# API Routes
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/form-handler.php [QSA,L]

# Protect sensitive files
<Files "*.php">
    <RequireAll>
        Require all denied
        Require local
    </RequireAll>
</Files>

# Allow API access
<Files "form-handler.php">
    Require all granted
</Files>

# Protect configuration files
<FilesMatch "\.(env|ini|conf|config)$">
    Require all denied
</FilesMatch>

# Protect database files
<FilesMatch "\.(sql|db|sqlite)$">
    Require all denied
</FilesMatch>

# File Upload Security
<Directory "uploads">
    # Prevent execution of uploaded files
    <FilesMatch "\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$">
        Require all denied
    </FilesMatch>
    
    # Only allow specific file types
    <FilesMatch "\.(jpg|jpeg|png|gif|pdf|doc|docx)$">
        Require all granted
    </FilesMatch>
    
    # Prevent directory browsing
    Options -Indexes
</Directory>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# PHP Configuration
<IfModule mod_php7.c>
    # File upload limits
    php_value upload_max_filesize 5M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_time 300
    
    # Session security
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_strict_mode 1
    
    # Error handling
    php_value display_errors 0
    php_value log_errors 1
    php_value error_log logs/php_errors.log
</IfModule>

# Prevent access to version control
<DirectoryMatch "\.git">
    Require all denied
</DirectoryMatch>

# Prevent access to node_modules
<DirectoryMatch "node_modules">
    Require all denied
</DirectoryMatch>

# Error Pages
ErrorDocument 404 /RegisterOnline/404.html
ErrorDocument 500 /RegisterOnline/500.html
