@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  [dir="rtl"] {
    font-family: 'Noto Sans Arabic', Arial, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors;
  }
  
  .form-input-error {
    @apply border-red-300 focus:ring-red-500 focus:border-red-500;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
  
  .form-error {
    @apply text-red-600 text-sm mt-1;
  }
  
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium py-2 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .step-indicator {
    @apply flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium transition-colors;
  }
  
  .step-indicator.completed {
    @apply bg-green-500 text-white;
  }
  
  .step-indicator.current {
    @apply bg-primary-600 text-white;
  }
  
  .step-indicator.pending {
    @apply bg-gray-200 text-gray-600;
  }
  
  .step-indicator.disabled {
    @apply bg-gray-100 text-gray-400 cursor-not-allowed;
  }
}

@layer utilities {
  .rtl {
    direction: rtl;
  }
  
  .ltr {
    direction: ltr;
  }
}
