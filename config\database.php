<?php
/**
 * Database Configuration for Bank Registration System
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'bank_registration';
    private $username = 'root';
    private $password = '';
    private $charset = 'utf8mb4';
    private $pdo;
    
    public function __construct() {
        $this->connect();
    }
    
    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->db_name};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];
            
            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
        } catch (PDOException $e) {
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }
    
    public function getConnection() {
        return $this->pdo;
    }
    
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }
    
    public function commit() {
        return $this->pdo->commit();
    }
    
    public function rollback() {
        return $this->pdo->rollback();
    }
    
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
}

/**
 * Application Configuration
 */
class Config {
    const UPLOAD_DIR = 'uploads/';
    const MAX_FILE_SIZE = 5242880; // 5MB
    const ALLOWED_FILE_TYPES = ['jpg', 'jpeg', 'png', 'pdf'];
    const SESSION_TIMEOUT = 3600; // 1 hour
    const CSRF_TOKEN_NAME = 'csrf_token';
    
    // Security settings
    const ENABLE_CSRF_PROTECTION = true;
    const ENABLE_XSS_PROTECTION = true;
    const ENABLE_SQL_INJECTION_PROTECTION = true;
    
    // Form validation rules
    const VALIDATION_RULES = [
        'personal_info' => [
            'first_name' => ['required', 'string', 'max:100'],
            'last_name' => ['required', 'string', 'max:100'],
            'gender' => ['required', 'in:male,female,other'],
            'nationality' => ['required', 'string', 'max:100'],
            'date_of_birth' => ['required', 'date', 'before:today'],
            'marital_status' => ['required', 'in:single,married,divorced,widowed']
        ],
        'birth_info' => [
            'country_of_birth' => ['required', 'string', 'max:100'],
            'city_of_birth' => ['required', 'string', 'max:100']
        ],
        'identity_info' => [
            'id_type' => ['required', 'in:national_id,passport,drivers_license'],
            'id_number' => ['required', 'string', 'max:50'],
            'id_expiry_date' => ['required', 'date', 'after:today'],
            'issuing_authority' => ['required', 'string', 'max:100']
        ],
        'residence_employment' => [
            'street_address' => ['required', 'string', 'max:255'],
            'city' => ['required', 'string', 'max:100'],
            'postal_code' => ['required', 'string', 'max:20'],
            'country' => ['required', 'string', 'max:100'],
            'residence_type' => ['required', 'in:owned,rented,family'],
            'employment_status' => ['required', 'in:employed,self_employed,unemployed,retired,student'],
            'monthly_income_range' => ['required', 'in:0-1000,1001-5000,5001-10000,10001-25000,25001-50000,50000+']
        ],
        'beneficial_owner' => [
            'account_purpose' => ['required', 'string'],
            'source_of_funds' => ['required', 'in:salary,business,investment,inheritance,gift,other'],
            'expected_transaction_volume' => ['required', 'in:low,medium,high']
        ],
        'contact_info' => [
            'primary_phone' => ['required', 'string', 'max:20'],
            'email' => ['required', 'email', 'max:255'],
            'preferred_communication' => ['required', 'in:email,phone,sms,mail']
        ],
        'political_influence' => [
            'is_pep' => ['required', 'boolean'],
            'family_member_pep' => ['required', 'boolean'],
            'sanctions_list_check' => ['required', 'boolean']
        ],
        'signature_final' => [
            'terms_accepted' => ['required', 'boolean', 'accepted'],
            'privacy_policy_accepted' => ['required', 'boolean', 'accepted'],
            'final_review_completed' => ['required', 'boolean', 'accepted'],
            'submission_confirmed' => ['required', 'boolean', 'accepted']
        ]
    ];
    
    public static function getUploadPath() {
        $uploadDir = __DIR__ . '/../' . self::UPLOAD_DIR;
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        return $uploadDir;
    }
    
    public static function generateCSRFToken() {
        if (!isset($_SESSION[self::CSRF_TOKEN_NAME])) {
            $_SESSION[self::CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
        }
        return $_SESSION[self::CSRF_TOKEN_NAME];
    }
    
    public static function validateCSRFToken($token) {
        return isset($_SESSION[self::CSRF_TOKEN_NAME]) && 
               hash_equals($_SESSION[self::CSRF_TOKEN_NAME], $token);
    }
}

// Initialize session with security settings
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
    ini_set('session.use_strict_mode', 1);
    session_start();
}

// Set security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');
