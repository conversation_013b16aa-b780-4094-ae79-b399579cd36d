import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { apiService } from '../services/api'

export interface FormData {
  personal_info?: {
    first_name?: string
    middle_name?: string
    last_name?: string
    gender?: 'male' | 'female' | 'other'
    nationality?: string
    date_of_birth?: string
    marital_status?: 'single' | 'married' | 'divorced' | 'widowed'
  }
  birth_info?: {
    country_of_birth?: string
    city_of_birth?: string
    birth_certificate_number?: string
  }
  identity_info?: {
    id_type?: 'national_id' | 'passport' | 'drivers_license'
    id_number?: string
    id_expiry_date?: string
    issuing_authority?: string
    id_document_path?: string
  }
  residence_employment?: {
    street_address?: string
    city?: string
    postal_code?: string
    country?: string
    residence_type?: 'owned' | 'rented' | 'family'
    employment_status?: 'employed' | 'self_employed' | 'unemployed' | 'retired' | 'student'
    employer_name?: string
    employer_address?: string
    monthly_income_range?: '0-1000' | '1001-5000' | '5001-10000' | '10001-25000' | '25001-50000' | '50000+'
    job_title?: string
    industry?: string
  }
  beneficial_owner?: {
    account_purpose?: string
    is_beneficial_owner_different?: boolean
    beneficial_owner_name?: string
    beneficial_owner_details?: string
    source_of_funds?: 'salary' | 'business' | 'investment' | 'inheritance' | 'gift' | 'other'
    expected_transaction_volume?: 'low' | 'medium' | 'high'
    business_relationship_details?: string
  }
  contact_info?: {
    primary_phone?: string
    secondary_phone?: string
    email?: string
    preferred_communication?: 'email' | 'phone' | 'sms' | 'mail'
    mailing_address_different?: boolean
    mailing_street_address?: string
    mailing_city?: string
    mailing_postal_code?: string
    mailing_country?: string
  }
  political_influence?: {
    is_pep?: boolean
    pep_details?: string
    family_member_pep?: boolean
    family_pep_details?: string
    government_position_history?: string
    political_party_affiliations?: string
    sanctions_list_check?: boolean
  }
  signature_final?: {
    digital_signature_path?: string
    terms_accepted?: boolean
    privacy_policy_accepted?: boolean
    final_review_completed?: boolean
    submission_confirmed?: boolean
  }
}

interface FormState {
  sessionId: string | null
  registrationId: number | null
  currentStep: number
  formData: FormData
  completedSteps: number[]
  isLoading: boolean
  error: string | null
  csrfToken: string | null
}

type FormAction =
  | { type: 'SET_SESSION'; payload: { sessionId: string; registrationId: number; csrfToken: string } }
  | { type: 'SET_CURRENT_STEP'; payload: number }
  | { type: 'UPDATE_STEP_DATA'; payload: { step: number; data: any } }
  | { type: 'MARK_STEP_COMPLETED'; payload: number }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'LOAD_FORM_DATA'; payload: { currentStep: number; data: FormData } }
  | { type: 'RESET_FORM' }

const initialState: FormState = {
  sessionId: null,
  registrationId: null,
  currentStep: 1,
  formData: {},
  completedSteps: [],
  isLoading: false,
  error: null,
  csrfToken: null
}

function formReducer(state: FormState, action: FormAction): FormState {
  switch (action.type) {
    case 'SET_SESSION':
      return {
        ...state,
        sessionId: action.payload.sessionId,
        registrationId: action.payload.registrationId,
        csrfToken: action.payload.csrfToken
      }
    case 'SET_CURRENT_STEP':
      return {
        ...state,
        currentStep: action.payload
      }
    case 'UPDATE_STEP_DATA':
      const stepKey = getStepKey(action.payload.step)
      return {
        ...state,
        formData: {
          ...state.formData,
          [stepKey]: action.payload.data
        }
      }
    case 'MARK_STEP_COMPLETED':
      return {
        ...state,
        completedSteps: [...new Set([...state.completedSteps, action.payload])]
      }
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload
      }
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload
      }
    case 'LOAD_FORM_DATA':
      return {
        ...state,
        currentStep: action.payload.currentStep,
        formData: action.payload.data,
        completedSteps: Object.keys(action.payload.data).map((_, index) => index + 1)
      }
    case 'RESET_FORM':
      return initialState
    default:
      return state
  }
}

function getStepKey(step: number): keyof FormData {
  const stepKeys: Record<number, keyof FormData> = {
    1: 'personal_info',
    2: 'birth_info',
    3: 'identity_info',
    4: 'residence_employment',
    5: 'beneficial_owner',
    6: 'contact_info',
    7: 'political_influence',
    8: 'signature_final'
  }
  return stepKeys[step]
}

interface FormContextType {
  state: FormState
  startRegistration: () => Promise<void>
  saveStep: (step: number, data: any) => Promise<void>
  goToStep: (step: number) => void
  loadFormData: () => Promise<void>
  uploadFile: (file: File, fileType: string, step: number) => Promise<string>
  submitForm: () => Promise<void>
  resetForm: () => void
}

const FormContext = createContext<FormContextType | undefined>(undefined)

export const FormProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(formReducer, initialState)

  useEffect(() => {
    // Load session from localStorage on mount
    const savedSessionId = localStorage.getItem('bank_registration_session')
    if (savedSessionId) {
      dispatch({ type: 'SET_SESSION', payload: { 
        sessionId: savedSessionId, 
        registrationId: 0, 
        csrfToken: '' 
      }})
      loadFormData()
    }
  }, [])

  const startRegistration = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      dispatch({ type: 'SET_ERROR', payload: null })
      
      const response = await apiService.startRegistration()
      
      dispatch({ type: 'SET_SESSION', payload: {
        sessionId: response.session_id,
        registrationId: response.registration_id,
        csrfToken: response.csrf_token
      }})
      
      localStorage.setItem('bank_registration_session', response.session_id)
      
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to start registration' })
      throw error
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }

  const saveStep = async (step: number, data: any) => {
    if (!state.sessionId) {
      throw new Error('No active session')
    }

    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      dispatch({ type: 'SET_ERROR', payload: null })
      
      await apiService.saveStep(state.sessionId, step, data, state.csrfToken!)
      
      dispatch({ type: 'UPDATE_STEP_DATA', payload: { step, data } })
      dispatch({ type: 'MARK_STEP_COMPLETED', payload: step })
      
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to save step data' })
      throw error
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }

  const goToStep = (step: number) => {
    dispatch({ type: 'SET_CURRENT_STEP', payload: step })
  }

  const loadFormData = async () => {
    if (!state.sessionId) return

    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      
      const response = await apiService.getFormData(state.sessionId)
      
      dispatch({ type: 'LOAD_FORM_DATA', payload: {
        currentStep: response.current_step,
        data: response.data
      }})
      
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load form data' })
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }

  const uploadFile = async (file: File, fileType: string, step: number): Promise<string> => {
    if (!state.sessionId) {
      throw new Error('No active session')
    }

    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      
      const response = await apiService.uploadFile(file, fileType, step, state.sessionId)
      
      return response.file_path
      
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to upload file' })
      throw error
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }

  const submitForm = async () => {
    if (!state.sessionId) {
      throw new Error('No active session')
    }

    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      dispatch({ type: 'SET_ERROR', payload: null })
      
      await apiService.submitForm(state.sessionId, state.csrfToken!)
      
      // Clear session after successful submission
      localStorage.removeItem('bank_registration_session')
      dispatch({ type: 'RESET_FORM' })
      
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to submit form' })
      throw error
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }

  const resetForm = () => {
    localStorage.removeItem('bank_registration_session')
    dispatch({ type: 'RESET_FORM' })
  }

  const value: FormContextType = {
    state,
    startRegistration,
    saveStep,
    goToStep,
    loadFormData,
    uploadFile,
    submitForm,
    resetForm
  }

  return (
    <FormContext.Provider value={value}>
      {children}
    </FormContext.Provider>
  )
}

export const useForm = () => {
  const context = useContext(FormContext)
  if (context === undefined) {
    throw new Error('useForm must be used within a FormProvider')
  }
  return context
}
