import React, { useEffect, useState } from 'react'
import { useLanguage } from '../../contexts/LanguageContext'
import { useForm } from '../../contexts/FormContext'
import FormCheckbox from '../FormCheckbox'
import FileUpload from '../FileUpload'

interface SignatureFinalData {
  digital_signature_path?: string
  terms_accepted?: boolean
  privacy_policy_accepted?: boolean
  final_review_completed?: boolean
  submission_confirmed?: boolean
}

interface SignatureFinalStepProps {
  data: SignatureFinalData
  onChange: (data: SignatureFinalData) => void
  errors: Record<string, string[]>
}

const SignatureFinalStep: React.FC<SignatureFinalStepProps> = ({
  data,
  onChange,
  errors
}) => {
  const { t } = useLanguage()
  const { state } = useForm()
  const [formData, setFormData] = useState<SignatureFinalData>(data)

  useEffect(() => {
    setFormData(data)
  }, [data])

  useEffect(() => {
    onChange(formData)
  }, [formData, onChange])

  const handleChange = (field: keyof SignatureFinalData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const getFieldError = (field: string): string | undefined => {
    return errors[field]?.[0]
  }

  const allRequiredChecked = formData.terms_accepted && 
                            formData.privacy_policy_accepted && 
                            formData.final_review_completed && 
                            formData.submission_confirmed

  return (
    <div className="space-y-8">
      {/* Digital Signature */}
      <div className="space-y-6">
        <div className="border-b pb-2">
          <h3 className="text-lg font-medium text-gray-900">Digital Signature</h3>
          <p className="text-sm text-gray-600 mt-1">
            Please upload your digital signature or a clear image of your handwritten signature.
          </p>
        </div>

        <FileUpload
          label="Upload Signature"
          name="digital_signature"
          fileType="signature"
          step={8}
          value={formData.digital_signature_path || ''}
          onChange={(filePath) => handleChange('digital_signature_path', filePath)}
          error={getFieldError('digital_signature_path')}
          required
          accept=".jpg,.jpeg,.png"
          maxSize={2 * 1024 * 1024} // 2MB
        />
      </div>

      {/* Review Summary */}
      <div className="space-y-6">
        <div className="border-b pb-2">
          <h3 className="text-lg font-medium text-gray-900">Application Review</h3>
          <p className="text-sm text-gray-600 mt-1">
            Please review your application details before submission.
          </p>
        </div>

        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <h4 className="font-medium text-gray-900 mb-4">Application Summary</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Personal Information:</span>
              <span className="ml-2 text-green-600">
                {state.formData.personal_info ? '✓ Complete' : '✗ Incomplete'}
              </span>
            </div>
            <div>
              <span className="font-medium">Birth Information:</span>
              <span className="ml-2 text-green-600">
                {state.formData.birth_info ? '✓ Complete' : '✗ Incomplete'}
              </span>
            </div>
            <div>
              <span className="font-medium">Identity Information:</span>
              <span className="ml-2 text-green-600">
                {state.formData.identity_info ? '✓ Complete' : '✗ Incomplete'}
              </span>
            </div>
            <div>
              <span className="font-medium">Residence & Employment:</span>
              <span className="ml-2 text-green-600">
                {state.formData.residence_employment ? '✓ Complete' : '✗ Incomplete'}
              </span>
            </div>
            <div>
              <span className="font-medium">Beneficial Owner:</span>
              <span className="ml-2 text-green-600">
                {state.formData.beneficial_owner ? '✓ Complete' : '✗ Incomplete'}
              </span>
            </div>
            <div>
              <span className="font-medium">Contact Information:</span>
              <span className="ml-2 text-green-600">
                {state.formData.contact_info ? '✓ Complete' : '✗ Incomplete'}
              </span>
            </div>
            <div>
              <span className="font-medium">Political Influence:</span>
              <span className="ml-2 text-green-600">
                {state.formData.political_influence ? '✓ Complete' : '✗ Incomplete'}
              </span>
            </div>
            <div>
              <span className="font-medium">Signature:</span>
              <span className="ml-2 text-green-600">
                {formData.digital_signature_path ? '✓ Complete' : '✗ Incomplete'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Legal Agreements */}
      <div className="space-y-6">
        <div className="border-b pb-2">
          <h3 className="text-lg font-medium text-gray-900">Legal Agreements</h3>
          <p className="text-sm text-gray-600 mt-1">
            Please read and accept the following agreements to complete your application.
          </p>
        </div>

        <div className="space-y-4">
          <FormCheckbox
            label="I accept the Terms and Conditions"
            name="terms_accepted"
            checked={formData.terms_accepted || false}
            onChange={(value) => handleChange('terms_accepted', value)}
            error={getFieldError('terms_accepted')}
            required
            description={
              <span>
                I have read, understood, and agree to be bound by the{' '}
                <a href="#" className="text-primary-600 hover:text-primary-800 underline">
                  Terms and Conditions
                </a>{' '}
                of this bank account.
              </span>
            }
          />

          <FormCheckbox
            label="I accept the Privacy Policy"
            name="privacy_policy_accepted"
            checked={formData.privacy_policy_accepted || false}
            onChange={(value) => handleChange('privacy_policy_accepted', value)}
            error={getFieldError('privacy_policy_accepted')}
            required
            description={
              <span>
                I have read and understood the{' '}
                <a href="#" className="text-primary-600 hover:text-primary-800 underline">
                  Privacy Policy
                </a>{' '}
                and consent to the collection and processing of my personal data.
              </span>
            }
          />

          <FormCheckbox
            label="I confirm that I have reviewed all information"
            name="final_review_completed"
            checked={formData.final_review_completed || false}
            onChange={(value) => handleChange('final_review_completed', value)}
            error={getFieldError('final_review_completed')}
            required
            description="I confirm that I have carefully reviewed all the information provided in this application and certify that it is true, complete, and accurate to the best of my knowledge."
          />

          <FormCheckbox
            label="I authorize the submission of this application"
            name="submission_confirmed"
            checked={formData.submission_confirmed || false}
            onChange={(value) => handleChange('submission_confirmed', value)}
            error={getFieldError('submission_confirmed')}
            required
            description="I authorize the bank to process this application and understand that providing false information may result in application rejection or account closure."
          />
        </div>
      </div>

      {/* Final Submission Notice */}
      <div className={`border rounded-lg p-4 ${allRequiredChecked ? 'bg-green-50 border-green-200' : 'bg-yellow-50 border-yellow-200'}`}>
        <div className="flex">
          <div className="flex-shrink-0">
            {allRequiredChecked ? (
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            ) : (
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            )}
          </div>
          <div className="ml-3">
            <h4 className={`text-sm font-medium ${allRequiredChecked ? 'text-green-800' : 'text-yellow-800'}`}>
              {allRequiredChecked ? 'Ready to Submit' : 'Complete Required Items'}
            </h4>
            <p className={`text-sm mt-1 ${allRequiredChecked ? 'text-green-700' : 'text-yellow-700'}`}>
              {allRequiredChecked 
                ? 'Your application is complete and ready for submission. Click the Submit button to send your application for review.'
                : 'Please complete all required items above before submitting your application.'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SignatureFinalStep
