import React from 'react'
import { useLanguage } from '../contexts/LanguageContext'
import { ChevronDown } from 'lucide-react'

interface Option {
  value: string
  label: string
}

interface FormSelectProps {
  label: string
  name: string
  value: string
  onChange: (value: string) => void
  options: Option[]
  error?: string
  required?: boolean
  placeholder?: string
  disabled?: boolean
  className?: string
}

const FormSelect: React.FC<FormSelectProps> = ({
  label,
  name,
  value,
  onChange,
  options,
  error,
  required = false,
  placeholder = 'Select an option',
  disabled = false,
  className = ''
}) => {
  const { isRTL } = useLanguage()

  return (
    <div className={`${className}`}>
      <label htmlFor={name} className="form-label">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      <div className="relative">
        <select
          id={name}
          name={name}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          required={required}
          className={`
            form-input appearance-none
            ${error ? 'form-input-error' : ''}
            ${isRTL ? 'text-right pr-10' : 'text-left pl-3 pr-10'}
            ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}
          `}
          dir={isRTL ? 'rtl' : 'ltr'}
        >
          <option value="" disabled>
            {placeholder}
          </option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        <div className={`
          absolute inset-y-0 ${isRTL ? 'left-0 pl-3' : 'right-0 pr-3'} 
          flex items-center pointer-events-none
        `}>
          <ChevronDown className="h-4 w-4 text-gray-400" />
        </div>
      </div>
      {error && <p className="form-error">{error}</p>}
    </div>
  )
}

export default FormSelect
