import React from 'react'
import { <PERSON><PERSON>er<PERSON>outer as Router, Routes, Route } from 'react-router-dom'
import { FormProvider } from './contexts/FormContext'
import { LanguageProvider } from './contexts/LanguageContext'
import RegistrationForm from './components/RegistrationForm'
import './App.css'

function App() {
  return (
    <LanguageProvider>
      <FormProvider>
        <Router>
          <div className="min-h-screen bg-gray-50">
            <Routes>
              <Route path="/" element={<RegistrationForm />} />
            </Routes>
          </div>
        </Router>
      </FormProvider>
    </LanguageProvider>
  )
}

export default App
