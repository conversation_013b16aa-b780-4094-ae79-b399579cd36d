import React, { useEffect, useState } from 'react'
import { useLanguage } from '../../contexts/LanguageContext'
import FormInput from '../FormInput'
import FormSelect from '../FormSelect'
import FormTextarea from '../FormTextarea'
import FormCheckbox from '../FormCheckbox'

interface BeneficialOwnerData {
  account_purpose?: string
  is_beneficial_owner_different?: boolean
  beneficial_owner_name?: string
  beneficial_owner_details?: string
  source_of_funds?: 'salary' | 'business' | 'investment' | 'inheritance' | 'gift' | 'other'
  expected_transaction_volume?: 'low' | 'medium' | 'high'
  business_relationship_details?: string
}

interface BeneficialOwnerStepProps {
  data: BeneficialOwnerData
  onChange: (data: BeneficialOwnerData) => void
  errors: Record<string, string[]>
}

const BeneficialOwnerStep: React.FC<BeneficialOwnerStepProps> = ({
  data,
  onChange,
  errors
}) => {
  const { t } = useLanguage()
  const [formData, setFormData] = useState<BeneficialOwnerData>(data)

  useEffect(() => {
    setFormData(data)
  }, [data])

  useEffect(() => {
    onChange(formData)
  }, [formData, onChange])

  const handleChange = (field: keyof BeneficialOwnerData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const getFieldError = (field: string): string | undefined => {
    return errors[field]?.[0]
  }

  const sourceOfFundsOptions = [
    { value: 'salary', label: 'Salary/Employment Income' },
    { value: 'business', label: 'Business Income' },
    { value: 'investment', label: 'Investment Returns' },
    { value: 'inheritance', label: 'Inheritance' },
    { value: 'gift', label: 'Gift' },
    { value: 'other', label: 'Other' }
  ]

  const transactionVolumeOptions = [
    { value: 'low', label: 'Low (Under $10,000/month)' },
    { value: 'medium', label: 'Medium ($10,000 - $100,000/month)' },
    { value: 'high', label: 'High (Over $100,000/month)' }
  ]

  return (
    <div className="space-y-6">
      <FormTextarea
        label="Account Purpose"
        name="account_purpose"
        value={formData.account_purpose || ''}
        onChange={(value) => handleChange('account_purpose', value)}
        error={getFieldError('account_purpose')}
        required
        rows={3}
        maxLength={500}
        placeholder="Describe the intended use of this account (e.g., personal banking, business operations, savings)"
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormSelect
          label="Source of Funds"
          name="source_of_funds"
          value={formData.source_of_funds || ''}
          onChange={(value) => handleChange('source_of_funds', value as BeneficialOwnerData['source_of_funds'])}
          options={sourceOfFundsOptions}
          error={getFieldError('source_of_funds')}
          required
          placeholder="Select source of funds"
        />

        <FormSelect
          label="Expected Transaction Volume"
          name="expected_transaction_volume"
          value={formData.expected_transaction_volume || ''}
          onChange={(value) => handleChange('expected_transaction_volume', value as BeneficialOwnerData['expected_transaction_volume'])}
          options={transactionVolumeOptions}
          error={getFieldError('expected_transaction_volume')}
          required
          placeholder="Select expected volume"
        />
      </div>

      <FormCheckbox
        label="Is the beneficial owner different from the account holder?"
        name="is_beneficial_owner_different"
        checked={formData.is_beneficial_owner_different || false}
        onChange={(value) => handleChange('is_beneficial_owner_different', value)}
        error={getFieldError('is_beneficial_owner_different')}
        description="Check this if someone other than yourself will be the ultimate beneficial owner of this account"
      />

      {formData.is_beneficial_owner_different && (
        <div className="space-y-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h4 className="font-medium text-yellow-800">Beneficial Owner Information</h4>
          
          <FormInput
            label="Beneficial Owner Full Name"
            name="beneficial_owner_name"
            value={formData.beneficial_owner_name || ''}
            onChange={(value) => handleChange('beneficial_owner_name', value)}
            error={getFieldError('beneficial_owner_name')}
            required
            maxLength={200}
            placeholder="Full legal name of beneficial owner"
          />

          <FormTextarea
            label="Beneficial Owner Details"
            name="beneficial_owner_details"
            value={formData.beneficial_owner_details || ''}
            onChange={(value) => handleChange('beneficial_owner_details', value)}
            error={getFieldError('beneficial_owner_details')}
            required
            rows={4}
            maxLength={1000}
            placeholder="Provide detailed information about the beneficial owner including relationship to you, reason for beneficial ownership, etc."
          />
        </div>
      )}

      <FormTextarea
        label="Business Relationship Details"
        name="business_relationship_details"
        value={formData.business_relationship_details || ''}
        onChange={(value) => handleChange('business_relationship_details', value)}
        error={getFieldError('business_relationship_details')}
        rows={3}
        maxLength={500}
        placeholder="Describe your business relationship with the bank and any relevant business activities (optional)"
      />

      {/* Information Note */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h4 className="text-sm font-medium text-blue-800">Anti-Money Laundering (AML) Compliance</h4>
            <p className="text-sm text-blue-700 mt-1">
              This information is required for compliance with anti-money laundering regulations. 
              All information provided will be kept confidential and used only for regulatory compliance purposes.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BeneficialOwnerStep
