import React, { createContext, useContext, useState, useEffect } from 'react'

interface LanguageContextType {
  language: 'en' | 'ar'
  setLanguage: (lang: 'en' | 'ar') => void
  t: (key: string) => string
  isRTL: boolean
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

const translations = {
  en: {
    // Navigation
    'nav.back': 'Back',
    'nav.next': 'Next',
    'nav.submit': 'Submit',
    'nav.save_draft': 'Save Draft',
    
    // Steps
    'step.1.title': 'Personal Information',
    'step.2.title': 'Place and Date of Birth',
    'step.3.title': 'Identity Information',
    'step.4.title': 'Residence and Employment',
    'step.5.title': 'Beneficial Owner Information',
    'step.6.title': 'Contact Information',
    'step.7.title': 'Political Influence',
    'step.8.title': 'Signature',
    
    // Personal Information
    'personal.first_name': 'First Name',
    'personal.middle_name': 'Middle Name',
    'personal.last_name': 'Last Name',
    'personal.gender': 'Gender',
    'personal.gender.male': 'Male',
    'personal.gender.female': 'Female',
    'personal.gender.other': 'Other',
    'personal.nationality': 'Nationality',
    'personal.date_of_birth': 'Date of Birth',
    'personal.marital_status': 'Marital Status',
    'personal.marital_status.single': 'Single',
    'personal.marital_status.married': 'Married',
    'personal.marital_status.divorced': 'Divorced',
    'personal.marital_status.widowed': 'Widowed',
    
    // Birth Information
    'birth.country_of_birth': 'Country of Birth',
    'birth.city_of_birth': 'City/Province of Birth',
    'birth.certificate_number': 'Birth Certificate Number (Optional)',
    
    // Identity Information
    'identity.id_type': 'ID Type',
    'identity.id_type.national_id': 'National ID',
    'identity.id_type.passport': 'Passport',
    'identity.id_type.drivers_license': 'Driver\'s License',
    'identity.id_number': 'ID Number',
    'identity.id_expiry_date': 'ID Expiry Date',
    'identity.issuing_authority': 'Issuing Authority',
    'identity.document_upload': 'Upload ID Document',
    
    // Residence and Employment
    'residence.street_address': 'Street Address',
    'residence.city': 'City',
    'residence.postal_code': 'Postal Code',
    'residence.country': 'Country',
    'residence.residence_type': 'Residence Type',
    'residence.residence_type.owned': 'Owned',
    'residence.residence_type.rented': 'Rented',
    'residence.residence_type.family': 'Family',
    'employment.status': 'Employment Status',
    'employment.status.employed': 'Employed',
    'employment.status.self_employed': 'Self Employed',
    'employment.status.unemployed': 'Unemployed',
    'employment.status.retired': 'Retired',
    'employment.status.student': 'Student',
    'employment.employer_name': 'Employer Name',
    'employment.employer_address': 'Employer Address',
    'employment.monthly_income': 'Monthly Income Range',
    'employment.job_title': 'Job Title',
    'employment.industry': 'Industry',
    
    // Common
    'common.required': 'This field is required',
    'common.invalid_email': 'Please enter a valid email address',
    'common.invalid_date': 'Please enter a valid date',
    'common.loading': 'Loading...',
    'common.error': 'An error occurred',
    'common.success': 'Success',
    'common.yes': 'Yes',
    'common.no': 'No',
    'common.upload_file': 'Upload File',
    'common.file_uploaded': 'File uploaded successfully',
    'common.remove_file': 'Remove File',
    
    // Progress
    'progress.step_of': 'Step {current} of {total}',
    'progress.completed': 'Completed',
    'progress.current': 'Current',
    'progress.pending': 'Pending',
  },
  ar: {
    // Navigation
    'nav.back': 'السابق',
    'nav.next': 'التالي',
    'nav.submit': 'إرسال',
    'nav.save_draft': 'حفظ المسودة',
    
    // Steps
    'step.1.title': 'البيانات الشخصية',
    'step.2.title': 'محل و تاريخ الميلاد',
    'step.3.title': 'بيانات الهوية',
    'step.4.title': 'معلومات الاقامة و العمل',
    'step.5.title': 'المستفيد الحقيقي من الحساب',
    'step.6.title': 'بيانات الاتصال',
    'step.7.title': 'النفوذ السياسي',
    'step.8.title': 'التوقيع',
    
    // Personal Information
    'personal.first_name': 'الاسم الأول',
    'personal.middle_name': 'الاسم الأوسط',
    'personal.last_name': 'اسم العائلة',
    'personal.gender': 'الجنس',
    'personal.gender.male': 'ذكر',
    'personal.gender.female': 'أنثى',
    'personal.gender.other': 'آخر',
    'personal.nationality': 'الجنسية',
    'personal.date_of_birth': 'تاريخ الميلاد',
    'personal.marital_status': 'الحالة الاجتماعية',
    'personal.marital_status.single': 'أعزب',
    'personal.marital_status.married': 'متزوج',
    'personal.marital_status.divorced': 'مطلق',
    'personal.marital_status.widowed': 'أرمل',
    
    // Birth Information
    'birth.country_of_birth': 'بلد الميلاد',
    'birth.city_of_birth': 'مدينة/محافظة الميلاد',
    'birth.certificate_number': 'رقم شهادة الميلاد (اختياري)',
    
    // Identity Information
    'identity.id_type': 'نوع الهوية',
    'identity.id_type.national_id': 'الهوية الوطنية',
    'identity.id_type.passport': 'جواز السفر',
    'identity.id_type.drivers_license': 'رخصة القيادة',
    'identity.id_number': 'رقم الهوية',
    'identity.id_expiry_date': 'تاريخ انتهاء الهوية',
    'identity.issuing_authority': 'الجهة المصدرة',
    'identity.document_upload': 'رفع وثيقة الهوية',
    
    // Residence and Employment
    'residence.street_address': 'عنوان الشارع',
    'residence.city': 'المدينة',
    'residence.postal_code': 'الرمز البريدي',
    'residence.country': 'البلد',
    'residence.residence_type': 'نوع السكن',
    'residence.residence_type.owned': 'ملك',
    'residence.residence_type.rented': 'مستأجر',
    'residence.residence_type.family': 'عائلي',
    'employment.status': 'حالة التوظيف',
    'employment.status.employed': 'موظف',
    'employment.status.self_employed': 'عمل حر',
    'employment.status.unemployed': 'عاطل عن العمل',
    'employment.status.retired': 'متقاعد',
    'employment.status.student': 'طالب',
    'employment.employer_name': 'اسم جهة العمل',
    'employment.employer_address': 'عنوان جهة العمل',
    'employment.monthly_income': 'نطاق الدخل الشهري',
    'employment.job_title': 'المسمى الوظيفي',
    'employment.industry': 'القطاع',
    
    // Common
    'common.required': 'هذا الحقل مطلوب',
    'common.invalid_email': 'يرجى إدخال بريد إلكتروني صحيح',
    'common.invalid_date': 'يرجى إدخال تاريخ صحيح',
    'common.loading': 'جاري التحميل...',
    'common.error': 'حدث خطأ',
    'common.success': 'نجح',
    'common.yes': 'نعم',
    'common.no': 'لا',
    'common.upload_file': 'رفع ملف',
    'common.file_uploaded': 'تم رفع الملف بنجاح',
    'common.remove_file': 'إزالة الملف',
    
    // Progress
    'progress.step_of': 'الخطوة {current} من {total}',
    'progress.completed': 'مكتمل',
    'progress.current': 'الحالي',
    'progress.pending': 'في الانتظار',
  }
}

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<'en' | 'ar'>('en')

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') as 'en' | 'ar'
    if (savedLanguage && ['en', 'ar'].includes(savedLanguage)) {
      setLanguage(savedLanguage)
    }
  }, [])

  useEffect(() => {
    localStorage.setItem('language', language)
    document.documentElement.lang = language
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr'
  }, [language])

  const t = (key: string): string => {
    const keys = key.split('.')
    let value: any = translations[language]
    
    for (const k of keys) {
      value = value?.[k]
    }
    
    return value || key
  }

  const value: LanguageContextType = {
    language,
    setLanguage,
    t,
    isRTL: language === 'ar'
  }

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  )
}

export const useLanguage = () => {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}
