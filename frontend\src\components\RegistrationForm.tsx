import React, { useEffect, useState } from 'react'
import { useForm } from '../contexts/FormContext'
import { useLanguage } from '../contexts/LanguageContext'
import StepIndicator from './StepIndicator'
import PersonalInfoStep from './steps/PersonalInfoStep'
import BirthInfoStep from './steps/BirthInfoStep'
import IdentityInfoStep from './steps/IdentityInfoStep'
import ResidenceEmploymentStep from './steps/ResidenceEmploymentStep'
import BeneficialOwnerStep from './steps/BeneficialOwnerStep'
import ContactInfoStep from './steps/ContactInfoStep'
import PoliticalInfluenceStep from './steps/PoliticalInfluenceStep'
import SignatureFinalStep from './steps/SignatureFinalStep'
import LanguageToggle from './LanguageToggle'
import LoadingSpinner from './LoadingSpinner'
import ErrorMessage from './ErrorMessage'
import { ChevronLeft, ChevronRight, Save, Send } from 'lucide-react'

const TOTAL_STEPS = 8

const RegistrationForm: React.FC = () => {
  const { state, startRegistration, goToStep, saveStep, submitForm } = useForm()
  const { t, isRTL } = useLanguage()
  const [isInitialized, setIsInitialized] = useState(false)
  const [currentStepData, setCurrentStepData] = useState<any>({})
  const [stepErrors, setStepErrors] = useState<Record<string, string[]>>({})

  useEffect(() => {
    const initializeForm = async () => {
      if (!state.sessionId) {
        try {
          await startRegistration()
        } catch (error) {
          console.error('Failed to start registration:', error)
        }
      }
      setIsInitialized(true)
    }

    initializeForm()
  }, [state.sessionId, startRegistration])

  const handleStepDataChange = (data: any) => {
    setCurrentStepData(data)
    setStepErrors({}) // Clear errors when data changes
  }

  const handleNext = async () => {
    try {
      await saveStep(state.currentStep, currentStepData)
      
      if (state.currentStep < TOTAL_STEPS) {
        goToStep(state.currentStep + 1)
        setCurrentStepData({})
      }
    } catch (error: any) {
      if (error.errors) {
        setStepErrors(error.errors)
      }
    }
  }

  const handleBack = () => {
    if (state.currentStep > 1) {
      goToStep(state.currentStep - 1)
      setCurrentStepData({})
      setStepErrors({})
    }
  }

  const handleSaveDraft = async () => {
    try {
      await saveStep(state.currentStep, currentStepData)
      // Show success message
    } catch (error: any) {
      if (error.errors) {
        setStepErrors(error.errors)
      }
    }
  }

  const handleSubmit = async () => {
    try {
      // Save current step first
      await saveStep(state.currentStep, currentStepData)
      // Then submit the entire form
      await submitForm()
      // Show success message and redirect
    } catch (error: any) {
      if (error.errors) {
        setStepErrors(error.errors)
      }
    }
  }

  const canGoNext = () => {
    return state.completedSteps.includes(state.currentStep) || 
           Object.keys(currentStepData).length > 0
  }

  const canGoToStep = (step: number) => {
    if (step === 1) return true
    return state.completedSteps.includes(step - 1) || step <= state.currentStep
  }

  const renderCurrentStep = () => {
    const stepProps = {
      data: state.formData[getStepKey(state.currentStep)] || {},
      onChange: handleStepDataChange,
      errors: stepErrors
    }

    switch (state.currentStep) {
      case 1:
        return <PersonalInfoStep {...stepProps} />
      case 2:
        return <BirthInfoStep {...stepProps} />
      case 3:
        return <IdentityInfoStep {...stepProps} />
      case 4:
        return <ResidenceEmploymentStep {...stepProps} />
      case 5:
        return <BeneficialOwnerStep {...stepProps} />
      case 6:
        return <ContactInfoStep {...stepProps} />
      case 7:
        return <PoliticalInfluenceStep {...stepProps} />
      case 8:
        return <SignatureFinalStep {...stepProps} />
      default:
        return null
    }
  }

  const getStepKey = (step: number) => {
    const stepKeys = [
      'personal_info',
      'birth_info',
      'identity_info',
      'residence_employment',
      'beneficial_owner',
      'contact_info',
      'political_influence',
      'signature_final'
    ]
    return stepKeys[step - 1]
  }

  if (!isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    )
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">
                Bank Account Registration
              </h1>
            </div>
            <LanguageToggle />
          </div>
        </div>
      </header>

      {/* Progress Indicator */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <StepIndicator
            currentStep={state.currentStep}
            totalSteps={TOTAL_STEPS}
            completedSteps={state.completedSteps}
            onStepClick={(step) => canGoToStep(step) && goToStep(step)}
          />
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm border">
          {/* Step Header */}
          <div className="px-6 py-4 border-b">
            <h2 className="text-lg font-medium text-gray-900">
              {t(`step.${state.currentStep}.title`)}
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              {t('progress.step_of', { 
                current: state.currentStep.toString(), 
                total: TOTAL_STEPS.toString() 
              })}
            </p>
          </div>

          {/* Error Display */}
          {state.error && (
            <div className="px-6 py-4">
              <ErrorMessage message={state.error} />
            </div>
          )}

          {/* Step Content */}
          <div className="px-6 py-6">
            {state.isLoading ? (
              <div className="flex justify-center py-12">
                <LoadingSpinner />
              </div>
            ) : (
              <div className="fade-in">
                {renderCurrentStep()}
              </div>
            )}
          </div>

          {/* Navigation */}
          <div className="px-6 py-4 border-t bg-gray-50 flex justify-between items-center">
            <div className="flex space-x-3">
              {state.currentStep > 1 && (
                <button
                  type="button"
                  onClick={handleBack}
                  disabled={state.isLoading}
                  className="btn-secondary flex items-center space-x-2"
                >
                  {isRTL ? <ChevronRight size={16} /> : <ChevronLeft size={16} />}
                  <span>{t('nav.back')}</span>
                </button>
              )}
              
              <button
                type="button"
                onClick={handleSaveDraft}
                disabled={state.isLoading}
                className="btn-secondary flex items-center space-x-2"
              >
                <Save size={16} />
                <span>{t('nav.save_draft')}</span>
              </button>
            </div>

            <div>
              {state.currentStep < TOTAL_STEPS ? (
                <button
                  type="button"
                  onClick={handleNext}
                  disabled={state.isLoading || !canGoNext()}
                  className="btn-primary flex items-center space-x-2"
                >
                  <span>{t('nav.next')}</span>
                  {isRTL ? <ChevronLeft size={16} /> : <ChevronRight size={16} />}
                </button>
              ) : (
                <button
                  type="button"
                  onClick={handleSubmit}
                  disabled={state.isLoading || !canGoNext()}
                  className="btn-primary flex items-center space-x-2 bg-green-600 hover:bg-green-700"
                >
                  <Send size={16} />
                  <span>{t('nav.submit')}</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

export default RegistrationForm
