import React, { useEffect, useState } from 'react'
import { useLanguage } from '../../contexts/LanguageContext'
import FormInput from '../FormInput'
import FormSelect from '../FormSelect'

interface PersonalInfoData {
  first_name?: string
  middle_name?: string
  last_name?: string
  gender?: 'male' | 'female' | 'other'
  nationality?: string
  date_of_birth?: string
  marital_status?: 'single' | 'married' | 'divorced' | 'widowed'
}

interface PersonalInfoStepProps {
  data: PersonalInfoData
  onChange: (data: PersonalInfoData) => void
  errors: Record<string, string[]>
}

const PersonalInfoStep: React.FC<PersonalInfoStepProps> = ({
  data,
  onChange,
  errors
}) => {
  const { t } = useLanguage()
  const [formData, setFormData] = useState<PersonalInfoData>(data)

  useEffect(() => {
    setFormData(data)
  }, [data])

  useEffect(() => {
    onChange(formData)
  }, [formData, onChange])

  const handleChange = (field: keyof PersonalInfoData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const getFieldError = (field: string): string | undefined => {
    return errors[field]?.[0]
  }

  const genderOptions = [
    { value: 'male', label: t('personal.gender.male') },
    { value: 'female', label: t('personal.gender.female') },
    { value: 'other', label: t('personal.gender.other') }
  ]

  const maritalStatusOptions = [
    { value: 'single', label: t('personal.marital_status.single') },
    { value: 'married', label: t('personal.marital_status.married') },
    { value: 'divorced', label: t('personal.marital_status.divorced') },
    { value: 'widowed', label: t('personal.marital_status.widowed') }
  ]

  // Common nationalities - in a real app, this would come from an API
  const nationalityOptions = [
    { value: 'saudi', label: 'Saudi Arabian' },
    { value: 'uae', label: 'United Arab Emirates' },
    { value: 'kuwait', label: 'Kuwaiti' },
    { value: 'qatar', label: 'Qatari' },
    { value: 'bahrain', label: 'Bahraini' },
    { value: 'oman', label: 'Omani' },
    { value: 'jordan', label: 'Jordanian' },
    { value: 'lebanon', label: 'Lebanese' },
    { value: 'syria', label: 'Syrian' },
    { value: 'iraq', label: 'Iraqi' },
    { value: 'egypt', label: 'Egyptian' },
    { value: 'morocco', label: 'Moroccan' },
    { value: 'tunisia', label: 'Tunisian' },
    { value: 'algeria', label: 'Algerian' },
    { value: 'libya', label: 'Libyan' },
    { value: 'sudan', label: 'Sudanese' },
    { value: 'yemen', label: 'Yemeni' },
    { value: 'palestine', label: 'Palestinian' },
    { value: 'usa', label: 'American' },
    { value: 'uk', label: 'British' },
    { value: 'canada', label: 'Canadian' },
    { value: 'australia', label: 'Australian' },
    { value: 'germany', label: 'German' },
    { value: 'france', label: 'French' },
    { value: 'italy', label: 'Italian' },
    { value: 'spain', label: 'Spanish' },
    { value: 'india', label: 'Indian' },
    { value: 'pakistan', label: 'Pakistani' },
    { value: 'bangladesh', label: 'Bangladeshi' },
    { value: 'philippines', label: 'Filipino' },
    { value: 'indonesia', label: 'Indonesian' },
    { value: 'malaysia', label: 'Malaysian' },
    { value: 'singapore', label: 'Singaporean' },
    { value: 'thailand', label: 'Thai' },
    { value: 'other', label: 'Other' }
  ].sort((a, b) => a.label.localeCompare(b.label))

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <FormInput
          label={t('personal.first_name')}
          name="first_name"
          value={formData.first_name || ''}
          onChange={(value) => handleChange('first_name', value)}
          error={getFieldError('first_name')}
          required
          maxLength={100}
        />

        <FormInput
          label={t('personal.middle_name')}
          name="middle_name"
          value={formData.middle_name || ''}
          onChange={(value) => handleChange('middle_name', value)}
          error={getFieldError('middle_name')}
          maxLength={100}
        />

        <FormInput
          label={t('personal.last_name')}
          name="last_name"
          value={formData.last_name || ''}
          onChange={(value) => handleChange('last_name', value)}
          error={getFieldError('last_name')}
          required
          maxLength={100}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormSelect
          label={t('personal.gender')}
          name="gender"
          value={formData.gender || ''}
          onChange={(value) => handleChange('gender', value as PersonalInfoData['gender'])}
          options={genderOptions}
          error={getFieldError('gender')}
          required
          placeholder={t('personal.gender')}
        />

        <FormSelect
          label={t('personal.nationality')}
          name="nationality"
          value={formData.nationality || ''}
          onChange={(value) => handleChange('nationality', value)}
          options={nationalityOptions}
          error={getFieldError('nationality')}
          required
          placeholder={t('personal.nationality')}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormInput
          label={t('personal.date_of_birth')}
          name="date_of_birth"
          type="date"
          value={formData.date_of_birth || ''}
          onChange={(value) => handleChange('date_of_birth', value)}
          error={getFieldError('date_of_birth')}
          required
        />

        <FormSelect
          label={t('personal.marital_status')}
          name="marital_status"
          value={formData.marital_status || ''}
          onChange={(value) => handleChange('marital_status', value as PersonalInfoData['marital_status'])}
          options={maritalStatusOptions}
          error={getFieldError('marital_status')}
          required
          placeholder={t('personal.marital_status')}
        />
      </div>

      {/* Information Note */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-blue-700">
              Please ensure all information matches your official identification documents. 
              This information will be used for account verification and cannot be easily changed later.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PersonalInfoStep
