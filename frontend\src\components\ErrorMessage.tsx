import React from 'react'
import { AlertCircle, X } from 'lucide-react'

interface ErrorMessageProps {
  message: string
  errors?: Record<string, string[]>
  onDismiss?: () => void
  className?: string
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({
  message,
  errors,
  onDismiss,
  className = ''
}) => {
  return (
    <div className={`bg-red-50 border border-red-200 rounded-md p-4 ${className}`}>
      <div className="flex">
        <div className="flex-shrink-0">
          <AlertCircle className="h-5 w-5 text-red-400" />
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-red-800">
            {message}
          </h3>
          {errors && Object.keys(errors).length > 0 && (
            <div className="mt-2">
              <ul className="list-disc list-inside text-sm text-red-700 space-y-1">
                {Object.entries(errors).map(([field, fieldErrors]) =>
                  fieldErrors.map((error, index) => (
                    <li key={`${field}-${index}`}>
                      <span className="font-medium capitalize">
                        {field.replace(/_/g, ' ')}:
                      </span>{' '}
                      {error}
                    </li>
                  ))
                )}
              </ul>
            </div>
          )}
        </div>
        {onDismiss && (
          <div className="ml-auto pl-3">
            <div className="-mx-1.5 -my-1.5">
              <button
                type="button"
                onClick={onDismiss}
                className="inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-red-50 focus:ring-red-600"
              >
                <span className="sr-only">Dismiss</span>
                <X className="h-3 w-3" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ErrorMessage
