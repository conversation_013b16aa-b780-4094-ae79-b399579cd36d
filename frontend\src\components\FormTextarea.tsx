import React from 'react'
import { useLanguage } from '../contexts/LanguageContext'

interface FormTextareaProps {
  label: string
  name: string
  value: string
  onChange: (value: string) => void
  error?: string
  required?: boolean
  placeholder?: string
  disabled?: boolean
  className?: string
  rows?: number
  maxLength?: number
}

const FormTextarea: React.FC<FormTextareaProps> = ({
  label,
  name,
  value,
  onChange,
  error,
  required = false,
  placeholder,
  disabled = false,
  className = '',
  rows = 4,
  maxLength
}) => {
  const { isRTL } = useLanguage()

  return (
    <div className={`${className}`}>
      <label htmlFor={name} className="form-label">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      <textarea
        id={name}
        name={name}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        required={required}
        rows={rows}
        maxLength={maxLength}
        className={`
          form-input resize-none
          ${error ? 'form-input-error' : ''}
          ${isRTL ? 'text-right' : 'text-left'}
          ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}
        `}
        dir={isRTL ? 'rtl' : 'ltr'}
      />
      {maxLength && (
        <div className="flex justify-between items-center mt-1">
          {error && <p className="form-error flex-1">{error}</p>}
          <p className="text-xs text-gray-500 ml-auto">
            {value.length}/{maxLength}
          </p>
        </div>
      )}
      {!maxLength && error && <p className="form-error">{error}</p>}
    </div>
  )
}

export default FormTextarea
