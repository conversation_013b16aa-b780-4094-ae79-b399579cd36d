import React, { useEffect, useState } from 'react'
import { useLanguage } from '../../contexts/LanguageContext'
import FormCheckbox from '../FormCheckbox'
import FormTextarea from '../FormTextarea'

interface PoliticalInfluenceData {
  is_pep?: boolean
  pep_details?: string
  family_member_pep?: boolean
  family_pep_details?: string
  government_position_history?: string
  political_party_affiliations?: string
  sanctions_list_check?: boolean
}

interface PoliticalInfluenceStepProps {
  data: PoliticalInfluenceData
  onChange: (data: PoliticalInfluenceData) => void
  errors: Record<string, string[]>
}

const PoliticalInfluenceStep: React.FC<PoliticalInfluenceStepProps> = ({
  data,
  onChange,
  errors
}) => {
  const { t } = useLanguage()
  const [formData, setFormData] = useState<PoliticalInfluenceData>(data)

  useEffect(() => {
    setFormData(data)
  }, [data])

  useEffect(() => {
    onChange(formData)
  }, [formData, onChange])

  const handleChange = (field: keyof PoliticalInfluenceData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const getFieldError = (field: string): string | undefined => {
    return errors[field]?.[0]
  }

  return (
    <div className="space-y-8">
      {/* PEP Declaration */}
      <div className="space-y-6">
        <div className="border-b pb-2">
          <h3 className="text-lg font-medium text-gray-900">
            Politically Exposed Person (PEP) Declaration
          </h3>
          <p className="text-sm text-gray-600 mt-1">
            Please answer the following questions honestly. This information is required for regulatory compliance.
          </p>
        </div>

        <FormCheckbox
          label="Are you a Politically Exposed Person (PEP)?"
          name="is_pep"
          checked={formData.is_pep || false}
          onChange={(value) => handleChange('is_pep', value)}
          error={getFieldError('is_pep')}
          required
          description="A PEP is someone who holds or has held a prominent public function, such as a head of state, senior politician, senior government official, judicial or military official, senior executive of a state-owned corporation, or important political party official."
        />

        {formData.is_pep && (
          <FormTextarea
            label="PEP Details"
            name="pep_details"
            value={formData.pep_details || ''}
            onChange={(value) => handleChange('pep_details', value)}
            error={getFieldError('pep_details')}
            required
            rows={4}
            maxLength={1000}
            placeholder="Please provide details about your political position(s), including title, organization, dates of service, and current status."
          />
        )}

        <FormCheckbox
          label="Is any family member or close associate a Politically Exposed Person?"
          name="family_member_pep"
          checked={formData.family_member_pep || false}
          onChange={(value) => handleChange('family_member_pep', value)}
          error={getFieldError('family_member_pep')}
          required
          description="This includes immediate family members (spouse, children, parents, siblings) and known close associates."
        />

        {formData.family_member_pep && (
          <FormTextarea
            label="Family/Associate PEP Details"
            name="family_pep_details"
            value={formData.family_pep_details || ''}
            onChange={(value) => handleChange('family_pep_details', value)}
            error={getFieldError('family_pep_details')}
            required
            rows={4}
            maxLength={1000}
            placeholder="Please provide details about the family member or associate, including their relationship to you, their position, and organization."
          />
        )}
      </div>

      {/* Government and Political History */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 border-b pb-2">
          Government and Political History
        </h3>

        <FormTextarea
          label="Government Position History (if any)"
          name="government_position_history"
          value={formData.government_position_history || ''}
          onChange={(value) => handleChange('government_position_history', value)}
          error={getFieldError('government_position_history')}
          rows={3}
          maxLength={500}
          placeholder="List any current or former government positions, including dates and organizations. Enter 'None' if not applicable."
        />

        <FormTextarea
          label="Political Party Affiliations (if any)"
          name="political_party_affiliations"
          value={formData.political_party_affiliations || ''}
          onChange={(value) => handleChange('political_party_affiliations', value)}
          error={getFieldError('political_party_affiliations')}
          rows={3}
          maxLength={500}
          placeholder="List any current or former political party memberships or significant affiliations. Enter 'None' if not applicable."
        />
      </div>

      {/* Sanctions Declaration */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 border-b pb-2">
          Sanctions Declaration
        </h3>

        <FormCheckbox
          label="I confirm that I am not on any sanctions list"
          name="sanctions_list_check"
          checked={formData.sanctions_list_check || false}
          onChange={(value) => handleChange('sanctions_list_check', value)}
          error={getFieldError('sanctions_list_check')}
          required
          description="I declare that neither I nor any beneficial owner of this account appear on any sanctions list maintained by the UN, EU, US, or other relevant authorities."
        />
      </div>

      {/* Important Notice */}
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h4 className="text-sm font-medium text-red-800">Important Legal Notice</h4>
            <div className="mt-1 text-sm text-red-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Providing false information is a criminal offense and may result in account closure and legal action</li>
                <li>You must notify the bank immediately if your PEP status changes</li>
                <li>Enhanced due diligence procedures may apply to PEP accounts</li>
                <li>All information provided will be verified against official databases</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Regulatory Information */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h4 className="text-sm font-medium text-blue-800">Why We Ask These Questions</h4>
            <p className="text-sm text-blue-700 mt-1">
              These questions are required by international anti-money laundering (AML) and counter-terrorism financing (CTF) regulations. 
              The information helps us comply with legal requirements and protect the integrity of the financial system. 
              Your responses are confidential and used solely for regulatory compliance purposes.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PoliticalInfluenceStep
